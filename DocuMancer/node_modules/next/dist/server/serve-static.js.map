{"version": 3, "sources": ["../../src/server/serve-static.ts"], "names": ["getContentType", "getExtension", "serveStatic", "send", "mime", "define", "req", "res", "path", "opts", "Promise", "resolve", "reject", "on", "err", "Error", "code", "pipe", "extWithoutDot", "getType", "lookup", "contentType", "extension"], "mappings": ";;;;;;;;;;;;;;;;IA6BaA,cAAc;eAAdA;;IAKAC,YAAY;eAAZA;;IAzBGC,WAAW;eAAXA;;;6DARC;;;;;;AAEjB,gGAAgG;AAChG,0FAA0F;AAC1FC,aAAI,CAACC,IAAI,CAACC,MAAM,CAAC;IACf,cAAc;QAAC;KAAO;AACxB;AAEO,SAASH,YACdI,GAAoB,EACpBC,GAAmB,EACnBC,IAAY,EACZC,IAAiC;IAEjC,OAAO,IAAIC,QAAQ,CAACC,SAASC;QAC3BT,IAAAA,aAAI,EAACG,KAAKE,MAAMC,MACbI,EAAE,CAAC,aAAa;YACf,yCAAyC;YACzC,MAAMC,MAAW,IAAIC,MAAM;YAC3BD,IAAIE,IAAI,GAAG;YACXJ,OAAOE;QACT,GACCD,EAAE,CAAC,SAASD,QACZK,IAAI,CAACV,KACLM,EAAE,CAAC,UAAUF;IAClB;AACF;AAEO,MAAMX,iBACX,aAAaG,aAAI,CAACC,IAAI,GAClB,CAACc,gBAA0Bf,aAAI,CAACC,IAAI,CAACe,OAAO,CAACD,iBAC7C,CAACA,gBAA0B,AAACf,aAAI,CAACC,IAAI,CAASgB,MAAM,CAACF;AAEpD,MAAMjB,eACX,kBAAkBE,aAAI,CAACC,IAAI,GACvB,CAACiB,cAAwBlB,aAAI,CAACC,IAAI,CAACH,YAAY,CAACoB,eAChD,CAACA,cAAwB,AAAClB,aAAI,CAACC,IAAI,CAASkB,SAAS,CAACD"}