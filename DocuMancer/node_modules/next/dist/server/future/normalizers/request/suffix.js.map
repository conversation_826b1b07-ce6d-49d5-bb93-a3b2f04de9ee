{"version": 3, "sources": ["../../../../../src/server/future/normalizers/request/suffix.ts"], "names": ["SuffixPathnameNormalizer", "constructor", "suffix", "match", "pathname", "endsWith", "normalize", "matched", "substring", "length"], "mappings": ";;;;+BAEaA;;;eAAAA;;;AAAN,MAAMA;IACXC,YAAY,AAAiBC,MAAc,CAAE;aAAhBA,SAAAA;IAAiB;IAEvCC,MAAMC,QAAgB,EAAE;QAC7B,6DAA6D;QAC7D,IAAI,CAACA,SAASC,QAAQ,CAAC,IAAI,CAACH,MAAM,GAAG,OAAO;QAE5C,OAAO;IACT;IAEOI,UAAUF,QAAgB,EAAEG,OAAiB,EAAU;QAC5D,uEAAuE;QACvE,IAAI,CAACA,WAAW,CAAC,IAAI,CAACJ,KAAK,CAACC,WAAW,OAAOA;QAE9C,OAAOA,SAASI,SAAS,CAAC,GAAGJ,SAASK,MAAM,GAAG,IAAI,CAACP,MAAM,CAACO,MAAM;IACnE;AACF"}