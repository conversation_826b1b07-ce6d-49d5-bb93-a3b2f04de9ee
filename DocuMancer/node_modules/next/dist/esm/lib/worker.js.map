{"version": 3, "sources": ["../../src/lib/worker.ts"], "names": ["Worker", "JestWorker", "RESTARTED", "Symbol", "cleanupWorkers", "worker", "cur<PERSON><PERSON><PERSON>", "_workerPool", "_workers", "_child", "kill", "constructor", "worker<PERSON><PERSON>", "options", "_restarting", "timeout", "onRestart", "logger", "console", "farmOptions", "restartPromise", "resolveRestartPromise", "activeTasks", "_worker", "undefined", "process", "on", "close", "createWorker", "forkOptions", "env", "IS_NEXT_WORKER", "Promise", "resolve", "enableWorkerThreads", "code", "signal", "error", "exit", "getStdout", "pipe", "stdout", "getStderr", "stderr", "onHanging", "warn", "end", "then", "hanging<PERSON><PERSON>r", "onActivity", "clearTimeout", "setTimeout", "method", "exposedMethods", "startsWith", "args", "attempts", "result", "race", "bind", "Error"], "mappings": "AACA,SAASA,UAAUC,UAAU,QAAQ,iCAAgC;AAIrE,MAAMC,YAAYC,OAAO;AAEzB,MAAMC,iBAAiB,CAACC;QACG;IAAzB,KAAK,MAAMC,aAAc,EAAA,sBAAA,AAACD,OAAeE,WAAW,qBAA3B,oBAA6BC,QAAQ,KAAI,EAAE,CAE/D;YACHF;SAAAA,oBAAAA,UAAUG,MAAM,qBAAhBH,kBAAkBI,IAAI,CAAC;IACzB;AACF;AAEA,OAAO,MAAMV;IAIXW,YACEC,UAAkB,EAClBC,OAMC,CACD;aAXMC,cAAuB;QAY7B,IAAI,EAAEC,OAAO,EAAEC,SAAS,EAAEC,SAASC,OAAO,EAAE,GAAGC,aAAa,GAAGN;QAE/D,IAAIO;QACJ,IAAIC;QACJ,IAAIC,cAAc;QAElB,IAAI,CAACC,OAAO,GAAGC;QAEf,oDAAoD;QACpDC,QAAQC,EAAE,CAAC,QAAQ;YACjB,IAAI,CAACC,KAAK;QACZ;QAEA,MAAMC,eAAe;gBAMRT;YALX,IAAI,CAACI,OAAO,GAAG,IAAItB,WAAWW,YAAY;gBACxC,GAAGO,WAAW;gBACdU,aAAa;oBACX,GAAGV,YAAYU,WAAW;oBAC1BC,KAAK;wBACH,GAAKX,EAAAA,2BAAAA,YAAYU,WAAW,qBAAvBV,yBAAyBW,GAAG,KAAI,CAAC,CAAC;wBACvC,GAAGL,QAAQK,GAAG;wBACdC,gBAAgB;oBAClB;gBACF;YACF;YACA,IAAI,CAACjB,WAAW,GAAG;YACnBM,iBAAiB,IAAIY,QACnB,CAACC,UAAaZ,wBAAwBY;YAGxC;;;;;;;;OAQC,GACD,IAAI,CAACd,YAAYe,mBAAmB,EAAE;oBACd;gBAAtB,KAAK,MAAM7B,UAAW,EAAA,4BAAA,AAAC,IAAI,CAACkB,OAAO,CAAShB,WAAW,qBAAjC,0BAAmCC,QAAQ,KAC/D,EAAE,CAEC;wBACHH;qBAAAA,iBAAAA,OAAOI,MAAM,qBAAbJ,eAAeqB,EAAE,CAAC,QAAQ,CAACS,MAAMC;wBAC/B,IAAI,AAACD,CAAAA,QAASC,UAAUA,WAAW,QAAQ,KAAM,IAAI,CAACb,OAAO,EAAE;4BAC7DN,OAAOoB,KAAK,CACV,CAAC,uCAAuC,EAAEF,KAAK,aAAa,EAAEC,OAAO,CAAC;4BAGxE,2EAA2E;4BAC3E,IAAI,CAAC,IAAI,CAACtB,WAAW,EAAE;gCACrB,uGAAuG;gCACvGW,QAAQa,IAAI,CAACH,QAAQ;4BACvB;wBACF;oBACF;gBACF;YACF;YAEA,IAAI,CAACZ,OAAO,CAACgB,SAAS,GAAGC,IAAI,CAACf,QAAQgB,MAAM;YAC5C,IAAI,CAAClB,OAAO,CAACmB,SAAS,GAAGF,IAAI,CAACf,QAAQkB,MAAM;QAC9C;QACAf;QAEA,MAAMgB,YAAY;YAChB,MAAMvC,SAAS,IAAI,CAACkB,OAAO;YAC3B,IAAI,CAAClB,QAAQ;YACb,MAAM4B,UAAUZ;YAChBJ,OAAO4B,IAAI,CACT,CAAC,6DAA6D,EAC5D9B,UAAU,CAAC,IAAI,EAAEA,UAAU,KAAK,QAAQ,CAAC,GAAG,GAC7C,0DAA0D,CAAC;YAG9D,IAAI,CAACD,WAAW,GAAG;YAEnBT,OAAOyC,GAAG,GAAGC,IAAI,CAAC;gBAChBd,QAAQ/B;gBACR0B;YACF;QACF;QAEA,IAAIoB,eAAuC;QAE3C,MAAMC,aAAa;YACjB,IAAID,cAAcE,aAAaF;YAC/BA,eAAe1B,cAAc,KAAK6B,WAAWP,WAAW7B;QAC1D;QAEA,KAAK,MAAMqC,UAAUjC,YAAYkC,cAAc,CAAE;YAC/C,IAAID,OAAOE,UAAU,CAAC,MAAM;YAC3B,AAAC,IAAI,AAAQ,CAACF,OAAO,GAAGrC,UAErB,OAAO,GAAGwC;gBACRjC;gBACA,IAAI;oBACF,IAAIkC,WAAW;oBACf,OAAS;wBACPP;wBACA,MAAMQ,SAAS,MAAMzB,QAAQ0B,IAAI,CAAC;4BAC/B,IAAI,CAACnC,OAAO,AAAQ,CAAC6B,OAAO,IAAIG;4BACjCnC;yBACD;wBAED,IAAIqC,WAAWvD,WAAW,OAAOuD;wBACjC,IAAIzC,WAAWA,UAAUoC,QAAQG,MAAM,EAAEC;oBAC3C;gBACF,SAAU;oBACRlC;oBACA2B;gBACF;YACF,IACA,AAAC,IAAI,CAAC1B,OAAO,AAAQ,CAAC6B,OAAO,CAACO,IAAI,CAAC,IAAI,CAACpC,OAAO;QACrD;IACF;IAEAuB,MAAqC;QACnC,MAAMzC,SAAS,IAAI,CAACkB,OAAO;QAC3B,IAAI,CAAClB,QAAQ;YACX,MAAM,IAAIuD,MAAM;QAClB;QACAxD,eAAeC;QACf,IAAI,CAACkB,OAAO,GAAGC;QACf,OAAOnB,OAAOyC,GAAG;IACnB;IAEA;;GAEC,GACDnB,QAAc;QACZ,IAAI,IAAI,CAACJ,OAAO,EAAE;YAChBnB,eAAe,IAAI,CAACmB,OAAO;YAC3B,IAAI,CAACA,OAAO,CAACuB,GAAG;QAClB;IACF;AACF"}