{"version": 3, "sources": ["../../src/lib/turbopack-warning.ts"], "names": ["path", "loadConfig", "Log", "PHASE_DEVELOPMENT_SERVER", "PHASE_PRODUCTION_BUILD", "unsupportedTurbopackNextConfigOptions", "unsupportedProductionSpecificTurbopackNextConfigOptions", "validateTurboNextConfig", "dir", "isDev", "getPkgManager", "require", "getBabelConfigFile", "defaultConfig", "bold", "cyan", "red", "underline", "interopDefault", "unsupportedParts", "babelrc", "basename", "hasWebpackConfig", "hasTurboConfig", "unsupportedConfig", "rawNextConfig", "phase", "rawConfig", "flatten<PERSON>eys", "obj", "prefix", "keys", "key", "pre", "length", "Array", "isArray", "concat", "push", "getDeepValue", "split", "slice", "customKeys", "unsupportedKeys", "startsWith", "webpack", "isUnsupported", "some", "unsupported<PERSON>ey", "e", "error", "feedbackMessage", "warn", "map", "name", "join", "pkgManager", "process", "exit"], "mappings": "AACA,OAAOA,UAAU,OAAM;AACvB,OAAOC,gBAAgB,mBAAkB;AACzC,YAAYC,SAAS,sBAAqB;AAC1C,SACEC,wBAAwB,EACxBC,sBAAsB,QACjB,0BAAyB;AAEhC,MAAMC,wCAAwC;IAC5C,qBAAqB;IACrB,SAAS;IACT,sBAAsB;IAEtB,oCAAoC;IACpC,qCAAqC;IACrC,yCAAyC;IACzC,sBAAsB;IACtB;IACA,oBAAoB;IACpB;IACA,+BAA+B;IAC/B;IAEA,yBAAyB;IACzB,iCAAiC;IACjC,sCAAsC;IACtC;IACA,qCAAqC;IACrC,mCAAmC;IAEnC;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IAEA,6DAA6D;IAC7D;IACA;IACA;IACA,uEAAuE;IACvE;IACA;IACA;CACD;AAED,kEAAkE;AAClE,MAAMC,0DAA0D;IAC9D;IACA,wEAAwE;IACxE,iCAAiC;IACjC;CACD;AAED,iCAAiC;AACjC,OAAO,eAAeC,wBAAwB,EAC5CC,GAAG,EACHC,KAAK,EAIN;IACC,MAAM,EAAEC,aAAa,EAAE,GACrBC,QAAQ;IACV,MAAM,EAAEC,kBAAkB,EAAE,GAC1BD,QAAQ;IACV,MAAM,EAAEE,aAAa,EAAE,GACrBF,QAAQ;IACV,MAAM,EAAEG,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAEC,SAAS,EAAE,GAClCN,QAAQ;IACV,MAAM,EAAEO,cAAc,EAAE,GACtBP,QAAQ;IAEV,IAAIQ,mBAAmB;IACvB,IAAIC,UAAU,MAAMR,mBAAmBJ;IACvC,IAAIY,SAASA,UAAUpB,KAAKqB,QAAQ,CAACD;IAErC,IAAIE,mBAAmB;IACvB,IAAIC,iBAAiB;IAErB,IAAIC,oBAA8B,EAAE;IACpC,IAAIC,gBAA4B,CAAC;IAEjC,MAAMC,QAAQjB,QAAQN,2BAA2BC;IACjD,IAAI;QACFqB,gBAAgBP,eACd,MAAMjB,WAAWyB,OAAOlB,KAAK;YAC3BmB,WAAW;QACb;QAGF,IAAI,OAAOF,kBAAkB,YAAY;YACvCA,gBAAgB,AAACA,cAAsBC,OAAO;gBAC5Cb;YACF;QACF;QAEA,MAAMe,cAAc,CAACC,KAAUC,SAAiB,EAAE;YAChD,IAAIC,OAAiB,EAAE;YAEvB,IAAK,MAAMC,OAAOH,IAAK;gBACrB,IAAI,QAAOA,uBAAAA,GAAK,CAACG,IAAI,MAAK,aAAa;oBACrC;gBACF;gBAEA,MAAMC,MAAMH,OAAOI,MAAM,GAAG,CAAC,EAAEJ,OAAO,CAAC,CAAC,GAAG;gBAE3C,IACE,OAAOD,GAAG,CAACG,IAAI,KAAK,YACpB,CAACG,MAAMC,OAAO,CAACP,GAAG,CAACG,IAAI,KACvBH,GAAG,CAACG,IAAI,KAAK,MACb;oBACAD,OAAOA,KAAKM,MAAM,CAACT,YAAYC,GAAG,CAACG,IAAI,EAAEC,MAAMD;gBACjD,OAAO;oBACLD,KAAKO,IAAI,CAACL,MAAMD;gBAClB;YACF;YAEA,OAAOD;QACT;QAEA,MAAMQ,eAAe,CAACV,KAAUE;YAC9B,IAAI,OAAOA,SAAS,UAAU;gBAC5BA,OAAOA,KAAKS,KAAK,CAAC;YACpB;YACA,IAAIT,KAAKG,MAAM,KAAK,GAAG;gBACrB,OAAOL,uBAAAA,GAAK,CAACE,wBAAAA,IAAM,CAAC,EAAE,CAAC;YACzB;YACA,OAAOQ,aAAaV,uBAAAA,GAAK,CAACE,wBAAAA,IAAM,CAAC,EAAE,CAAC,EAAEA,KAAKU,KAAK,CAAC;QACnD;QAEA,MAAMC,aAAad,YAAYH;QAE/B,IAAIkB,kBAAkBlC,QAClBJ,wCACA;eACKA;eACAC;SACJ;QAEL,KAAK,MAAM0B,OAAOU,WAAY;YAC5B,IAAIV,IAAIY,UAAU,CAAC,cAAcnB,cAAcoB,OAAO,EAAE;gBACtDvB,mBAAmB;YACrB;YACA,IAAIU,IAAIY,UAAU,CAAC,uBAAuB;gBACxCrB,iBAAiB;YACnB;YAEA,IAAIuB,gBACFH,gBAAgBI,IAAI,CAClB,CAACC,iBACC,2DAA2D;gBAC3D,+DAA+D;gBAC/D,+BAA+B;gBAC/B,+BAA+B;gBAC/B,+BAA+B;gBAC/B,+BAA+B;gBAC/B,+BAA+B;gBAC/BhB,IAAIY,UAAU,CAACI,mBACfA,eAAeJ,UAAU,CAAC,CAAC,EAAEZ,IAAI,CAAC,CAAC,MAEvCO,aAAad,eAAeO,SAASO,aAAa1B,eAAemB;YAEnE,IAAIc,eAAe;gBACjBtB,kBAAkBc,IAAI,CAACN;YACzB;QACF;IACF,EAAE,OAAOiB,GAAG;QACV/C,IAAIgD,KAAK,CAAC,mDAAmDD;IAC/D;IAEA,MAAME,kBAAkB,CAAC,wCAAwC,EAAElC,UACjE,sCACA,EAAE,CAAC;IAEL,IAAIK,oBAAoB,CAACC,gBAAgB;QACvCrB,IAAIkD,IAAI,CACN,CAAC,uEAAuE,CAAC;QAE3ElD,IAAIkD,IAAI,CACN,CAAC,wHAAwH,CAAC;IAE9H;IAEA,IAAIhC,SAAS;QACXD,oBAAoB,CAAC,gBAAgB,EAAEJ,KACrCK,SACA,8GAA8G,CAAC;IACnH;IAEA,IACEI,kBAAkBU,MAAM,KAAK,KAC7BV,iBAAiB,CAAC,EAAE,KAAK,uCACzB;QACAtB,IAAIkD,IAAI,CACN,CAAC,4FAA4F,CAAC;IAElG,OAAO,IAAI5B,kBAAkBU,MAAM,EAAE;QACnCf,oBAAoB,CAAC,mDAAmD,EAAEJ,KACxE,kBACA,oEAAoE,EAAES,kBACrE6B,GAAG,CAAC,CAACC,OAAS,CAAC,MAAM,EAAEtC,IAAIsC,MAAM,EAAE,CAAC,EACpCC,IAAI,CAAC,IAAI,CAAC;IACf;IAEA,IAAIpC,kBAAkB;QACpB,MAAMqC,aAAa9C,cAAcF;QAEjCN,IAAIgD,KAAK,CACP,CAAC,iGAAiG,EAAE/B,iBAAiB;;;EAGzH,EAAEL,KACAC,KACE,CAAC,EACCyC,eAAe,QACX,wBACA,CAAC,EAAEA,WAAW,gBAAgB,CAAC,CACpC,4CAA4C,CAAC,GAEhD,6BAA6B,EAAEA,WAAW;QACtC,CAAC;QAGLtD,IAAIkD,IAAI,CAACD;QAETM,QAAQC,IAAI,CAAC;IACf;IAEA,OAAOjC;AACT"}