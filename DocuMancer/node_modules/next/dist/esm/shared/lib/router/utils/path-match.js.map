{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/path-match.ts"], "names": ["pathToRegexp", "regexpToFunction", "getPathMatch", "path", "options", "keys", "regexp", "delimiter", "sensitive", "strict", "matcher", "regexModifier", "RegExp", "source", "flags", "pathname", "params", "match", "removeUnnamedP<PERSON>ms", "key", "name"], "mappings": "AACA,SAASA,YAAY,QAAQ,oCAAmC;AAChE,SAASC,gBAAgB,QAAQ,oCAAmC;AA8BpE;;;;CAIC,GACD,OAAO,SAASC,aAAaC,IAAY,EAAEC,OAAiB;IAC1D,MAAMC,OAAc,EAAE;IACtB,MAAMC,SAASN,aAAaG,MAAME,MAAM;QACtCE,WAAW;QACXC,WACE,QAAOJ,2BAAAA,QAASI,SAAS,MAAK,YAAYJ,QAAQI,SAAS,GAAG;QAChEC,MAAM,EAAEL,2BAAAA,QAASK,MAAM;IACzB;IAEA,MAAMC,UAAUT,iBACdG,CAAAA,2BAAAA,QAASO,aAAa,IAClB,IAAIC,OAAOR,QAAQO,aAAa,CAACL,OAAOO,MAAM,GAAGP,OAAOQ,KAAK,IAC7DR,QACJD;IAGF;;;;;GAKC,GACD,OAAO,CAACU,UAAUC;QAChB,+CAA+C;QAC/C,IAAI,OAAOD,aAAa,UAAU,OAAO;QAEzC,MAAME,QAAQP,QAAQK;QAEtB,sDAAsD;QACtD,IAAI,CAACE,OAAO,OAAO;QAEnB;;;;KAIC,GACD,IAAIb,2BAAAA,QAASc,mBAAmB,EAAE;YAChC,KAAK,MAAMC,OAAOd,KAAM;gBACtB,IAAI,OAAOc,IAAIC,IAAI,KAAK,UAAU;oBAChC,OAAOH,MAAMD,MAAM,CAACG,IAAIC,IAAI,CAAC;gBAC/B;YACF;QACF;QAEA,OAAO;YAAE,GAAGJ,MAAM;YAAE,GAAGC,MAAMD,MAAM;QAAC;IACtC;AACF"}