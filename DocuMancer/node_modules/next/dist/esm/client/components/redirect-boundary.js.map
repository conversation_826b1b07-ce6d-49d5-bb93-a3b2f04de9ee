{"version": 3, "sources": ["../../../src/client/components/redirect-boundary.tsx"], "names": ["React", "useEffect", "useRouter", "RedirectType", "getRedirectTypeFromError", "getURLFromRedirectError", "isRedirectError", "HandleRedirect", "redirect", "reset", "redirectType", "router", "startTransition", "push", "replace", "RedirectErrorBoundary", "Component", "getDerivedStateFromError", "error", "url", "render", "state", "setState", "props", "children", "constructor", "RedirectBoundary"], "mappings": "AAAA;;AACA,OAAOA,SAASC,SAAS,QAAQ,QAAO;AAExC,SAASC,SAAS,QAAQ,eAAc;AACxC,SACEC,YAAY,EACZC,wBAAwB,EACxBC,uBAAuB,EACvBC,eAAe,QACV,aAAY;AAOnB,SAASC,eAAe,KAQvB;IARuB,IAAA,EACtBC,QAAQ,EACRC,KAAK,EACLC,YAAY,EAKb,GARuB;IAStB,MAAMC,SAAST;IAEfD,UAAU;QACRD,MAAMY,eAAe,CAAC;YACpB,IAAIF,iBAAiBP,aAAaU,IAAI,EAAE;gBACtCF,OAAOE,IAAI,CAACL,UAAU,CAAC;YACzB,OAAO;gBACLG,OAAOG,OAAO,CAACN,UAAU,CAAC;YAC5B;YACAC;QACF;IACF,GAAG;QAACD;QAAUE;QAAcD;QAAOE;KAAO;IAE1C,OAAO;AACT;AAEA,OAAO,MAAMI,8BAA8Bf,MAAMgB,SAAS;IASxD,OAAOC,yBAAyBC,KAAU,EAAE;QAC1C,IAAIZ,gBAAgBY,QAAQ;YAC1B,MAAMC,MAAMd,wBAAwBa;YACpC,MAAMR,eAAeN,yBAAyBc;YAC9C,OAAO;gBAAEV,UAAUW;gBAAKT;YAAa;QACvC;QACA,wCAAwC;QACxC,MAAMQ;IACR;IAEA,0IAA0I;IAC1IE,SAA0B;QACxB,MAAM,EAAEZ,QAAQ,EAAEE,YAAY,EAAE,GAAG,IAAI,CAACW,KAAK;QAC7C,IAAIb,aAAa,QAAQE,iBAAiB,MAAM;YAC9C,qBACE,KAACH;gBACCC,UAAUA;gBACVE,cAAcA;gBACdD,OAAO,IAAM,IAAI,CAACa,QAAQ,CAAC;wBAAEd,UAAU;oBAAK;;QAGlD;QAEA,OAAO,IAAI,CAACe,KAAK,CAACC,QAAQ;IAC5B;IA7BAC,YAAYF,KAA4B,CAAE;QACxC,KAAK,CAACA;QACN,IAAI,CAACF,KAAK,GAAG;YAAEb,UAAU;YAAME,cAAc;QAAK;IACpD;AA2BF;AAEA,OAAO,SAASgB,iBAAiB,KAA2C;IAA3C,IAAA,EAAEF,QAAQ,EAAiC,GAA3C;IAC/B,MAAMb,SAAST;IACf,qBACE,KAACa;QAAsBJ,QAAQA;kBAASa;;AAE5C"}