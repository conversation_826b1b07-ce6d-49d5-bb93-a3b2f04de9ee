{"version": 3, "sources": ["../../src/client/route-loader.ts"], "names": ["getAssetPathFromRoute", "__unsafeCreateTrustedScriptURL", "requestIdleCallback", "getDeploymentIdQueryOrEmptyString", "MS_MAX_IDLE_DELAY", "withFuture", "key", "map", "generator", "entry", "get", "future", "Promise", "resolve", "resolver", "prom", "set", "then", "value", "catch", "err", "delete", "ASSET_LOAD_ERROR", "Symbol", "<PERSON><PERSON><PERSON><PERSON>", "Object", "defineProperty", "isAssetError", "hasPrefetch", "link", "document", "createElement", "window", "MSInputMethodContext", "documentMode", "relList", "supports", "canPrefetch", "getAssetQueryString", "prefetchViaDom", "href", "as", "reject", "selector", "querySelector", "rel", "crossOrigin", "process", "env", "__NEXT_CROSS_ORIGIN", "onload", "onerror", "Error", "head", "append<PERSON><PERSON><PERSON>", "appendScript", "src", "script", "body", "devBuildPromise", "resolvePromiseWithTimeout", "p", "ms", "cancelled", "r", "NODE_ENV", "setTimeout", "getClientBuildManifest", "self", "__BUILD_MANIFEST", "onBuildManifest", "cb", "__BUILD_MANIFEST_CB", "getFilesForRoute", "assetPrefix", "route", "scriptUrl", "encodeURI", "scripts", "css", "manifest", "allFiles", "filter", "v", "endsWith", "createRouteLoader", "entrypoints", "Map", "loadedScripts", "styleSheets", "routes", "maybeExecuteScript", "toString", "fetchStyleSheet", "fetch", "credentials", "res", "ok", "text", "content", "whenEntrypoint", "onEntrypoint", "execute", "exports", "component", "default", "error", "undefined", "input", "old", "loadRoute", "prefetch", "devBuildPromiseResolve", "all", "has", "entrypoint", "styles", "assign", "finally", "cn", "navigator", "connection", "saveData", "test", "effectiveType", "output"], "mappings": "AAEA,OAAOA,2BAA2B,uDAAsD;AACxF,SAASC,8BAA8B,QAAQ,kBAAiB;AAChE,SAASC,mBAAmB,QAAQ,0BAAyB;AAC7D,SAASC,iCAAiC,QAAQ,yBAAwB;AAE1E,uEAAuE;AACvE,yEAAyE;AACzE,2EAA2E;AAC3E,oCAAoC;AACpC,MAAMC,oBAAoB;AA2C1B,SAASC,WACPC,GAAW,EACXC,GAA+B,EAC/BC,SAA4B;IAE5B,IAAIC,QAAQF,IAAIG,GAAG,CAACJ;IACpB,IAAIG,OAAO;QACT,IAAI,YAAYA,OAAO;YACrB,OAAOA,MAAME,MAAM;QACrB;QACA,OAAOC,QAAQC,OAAO,CAACJ;IACzB;IACA,IAAIK;IACJ,MAAMC,OAAmB,IAAIH,QAAW,CAACC;QACvCC,WAAWD;IACb;IACAN,IAAIS,GAAG,CAACV,KAAMG,QAAQ;QAAEI,SAASC;QAAWH,QAAQI;IAAK;IACzD,OAAOP,YACHA,WACE,wCAAwC;KACvCS,IAAI,CAAC,CAACC,QAAWJ,CAAAA,SAASI,QAAQA,KAAI,GACtCC,KAAK,CAAC,CAACC;QACNb,IAAIc,MAAM,CAACf;QACX,MAAMc;IACR,KACFL;AACN;AASA,MAAMO,mBAAmBC,OAAO;AAChC,iBAAiB;AACjB,OAAO,SAASC,eAAeJ,GAAU;IACvC,OAAOK,OAAOC,cAAc,CAACN,KAAKE,kBAAkB,CAAC;AACvD;AAEA,OAAO,SAASK,aAAaP,GAAW;IACtC,OAAOA,OAAOE,oBAAoBF;AACpC;AAEA,SAASQ,YAAYC,IAAsB;IACzC,IAAI;QACFA,OAAOC,SAASC,aAAa,CAAC;QAC9B,OAGE,AAFA,4DAA4D;QAC5D,uBAAuB;QACtB,CAAC,CAACC,OAAOC,oBAAoB,IAAI,CAAC,CAAC,AAACH,SAAiBI,YAAY,IAClEL,KAAKM,OAAO,CAACC,QAAQ,CAAC;IAE1B,EAAE,UAAM;QACN,OAAO;IACT;AACF;AAEA,MAAMC,cAAuBT;AAE7B,MAAMU,sBAAsB;IAC1B,OAAOnC;AACT;AAEA,SAASoC,eACPC,IAAY,EACZC,EAAU,EACVZ,IAAsB;IAEtB,OAAO,IAAIjB,QAAc,CAACC,SAAS6B;QACjC,MAAMC,WAAW,AAAC,yCACcH,OAAK,2CACNA,OAAK,6BACnBA,OAAK;QACtB,IAAIV,SAASc,aAAa,CAACD,WAAW;YACpC,OAAO9B;QACT;QAEAgB,OAAOC,SAASC,aAAa,CAAC;QAE9B,wDAAwD;QACxD,IAAIU,IAAIZ,KAAMY,EAAE,GAAGA;QACnBZ,KAAMgB,GAAG,GAAI;QACbhB,KAAMiB,WAAW,GAAGC,QAAQC,GAAG,CAACC,mBAAmB;QACnDpB,KAAMqB,MAAM,GAAGrC;QACfgB,KAAMsB,OAAO,GAAG,IACdT,OAAOlB,eAAe,IAAI4B,MAAM,AAAC,yBAAsBZ;QAEzD,gCAAgC;QAChCX,KAAMW,IAAI,GAAGA;QAEbV,SAASuB,IAAI,CAACC,WAAW,CAACzB;IAC5B;AACF;AAEA,SAAS0B,aACPC,GAA8B,EAC9BC,MAA0B;IAE1B,OAAO,IAAI7C,QAAQ,CAACC,SAAS6B;QAC3Be,SAAS3B,SAASC,aAAa,CAAC;QAEhC,wDAAwD;QACxD,mEAAmE;QACnE,iCAAiC;QACjC0B,OAAOP,MAAM,GAAGrC;QAChB4C,OAAON,OAAO,GAAG,IACfT,OAAOlB,eAAe,IAAI4B,MAAM,AAAC,4BAAyBI;QAE5D,2EAA2E;QAC3E,8BAA8B;QAC9BC,OAAOX,WAAW,GAAGC,QAAQC,GAAG,CAACC,mBAAmB;QAEpD,uEAAuE;QACvE,6CAA6C;QAC7CQ,OAAOD,GAAG,GAAGA;QACb1B,SAAS4B,IAAI,CAACJ,WAAW,CAACG;IAC5B;AACF;AAEA,4EAA4E;AAC5E,qEAAqE;AACrE,IAAIE;AAEJ,uEAAuE;AACvE,SAASC,0BACPC,CAAa,EACbC,EAAU,EACV1C,GAAU;IAEV,OAAO,IAAIR,QAAQ,CAACC,SAAS6B;QAC3B,IAAIqB,YAAY;QAEhBF,EAAE5C,IAAI,CAAC,CAAC+C;YACN,+BAA+B;YAC/BD,YAAY;YACZlD,QAAQmD;QACV,GAAG7C,KAAK,CAACuB;QAET,sEAAsE;QACtE,sBAAsB;QACtB,IAAIK,QAAQC,GAAG,CAACiB,QAAQ,KAAK,eAAe;YACxCN,CAAAA,mBAAmB/C,QAAQC,OAAO,EAAC,EAAGI,IAAI,CAAC;gBAC3Cf,oBAAoB,IAClBgE,WAAW;wBACT,IAAI,CAACH,WAAW;4BACdrB,OAAOtB;wBACT;oBACF,GAAG0C;YAEP;QACF;QAEA,IAAIf,QAAQC,GAAG,CAACiB,QAAQ,KAAK,eAAe;YAC1C/D,oBAAoB,IAClBgE,WAAW;oBACT,IAAI,CAACH,WAAW;wBACdrB,OAAOtB;oBACT;gBACF,GAAG0C;QAEP;IACF;AACF;AAEA,4CAA4C;AAC5C,4EAA4E;AAC5E,wEAAwE;AACxE,6EAA6E;AAC7E,2EAA2E;AAC3E,8BAA8B;AAC9B,OAAO,SAASK;IACd,IAAIC,KAAKC,gBAAgB,EAAE;QACzB,OAAOzD,QAAQC,OAAO,CAACuD,KAAKC,gBAAgB;IAC9C;IAEA,MAAMC,kBAAkB,IAAI1D,QAAkC,CAACC;QAC7D,iDAAiD;QACjD,MAAM0D,KAAKH,KAAKI,mBAAmB;QACnCJ,KAAKI,mBAAmB,GAAG;YACzB3D,QAAQuD,KAAKC,gBAAgB;YAC7BE,MAAMA;QACR;IACF;IAEA,OAAOX,0BACLU,iBACAlE,mBACAoB,eAAe,IAAI4B,MAAM;AAE7B;AAMA,SAASqB,iBACPC,WAAmB,EACnBC,KAAa;IAEb,IAAI5B,QAAQC,GAAG,CAACiB,QAAQ,KAAK,eAAe;QAC1C,MAAMW,YACJF,cACA,+BACAG,UAAU7E,sBAAsB2E,OAAO,UACvCrC;QACF,OAAO1B,QAAQC,OAAO,CAAC;YACrBiE,SAAS;gBAAC7E,+BAA+B2E;aAAW;YACpD,uDAAuD;YACvDG,KAAK,EAAE;QACT;IACF;IACA,OAAOZ,yBAAyBlD,IAAI,CAAC,CAAC+D;QACpC,IAAI,CAAEL,CAAAA,SAASK,QAAO,GAAI;YACxB,MAAMxD,eAAe,IAAI4B,MAAM,AAAC,6BAA0BuB;QAC5D;QACA,MAAMM,WAAWD,QAAQ,CAACL,MAAM,CAACpE,GAAG,CAClC,CAACE,QAAUiE,cAAc,YAAYG,UAAUpE;QAEjD,OAAO;YACLqE,SAASG,SACNC,MAAM,CAAC,CAACC,IAAMA,EAAEC,QAAQ,CAAC,QACzB7E,GAAG,CAAC,CAAC4E,IAAMlF,+BAA+BkF,KAAK7C;YAClDyC,KAAKE,SACFC,MAAM,CAAC,CAACC,IAAMA,EAAEC,QAAQ,CAAC,SACzB7E,GAAG,CAAC,CAAC4E,IAAMA,IAAI7C;QACpB;IACF;AACF;AAEA,OAAO,SAAS+C,kBAAkBX,WAAmB;IACnD,MAAMY,cACJ,IAAIC;IACN,MAAMC,gBAA+C,IAAID;IACzD,MAAME,cAAqD,IAAIF;IAC/D,MAAMG,SACJ,IAAIH;IAEN,SAASI,mBACPnC,GAA8B;QAE9B,2DAA2D;QAC3D,kEAAkE;QAClE,cAAc;QACd,IAAIT,QAAQC,GAAG,CAACiB,QAAQ,KAAK,eAAe;YAC1C,IAAIlD,OAAqCyE,cAAc9E,GAAG,CAAC8C,IAAIoC,QAAQ;YACvE,IAAI7E,MAAM;gBACR,OAAOA;YACT;YAEA,oDAAoD;YACpD,IAAIe,SAASc,aAAa,CAAC,AAAC,kBAAeY,MAAI,OAAM;gBACnD,OAAO5C,QAAQC,OAAO;YACxB;YAEA2E,cAAcxE,GAAG,CAACwC,IAAIoC,QAAQ,IAAK7E,OAAOwC,aAAaC;YACvD,OAAOzC;QACT,OAAO;YACL,OAAOwC,aAAaC;QACtB;IACF;IAEA,SAASqC,gBAAgBrD,IAAY;QACnC,IAAIzB,OAA6C0E,YAAY/E,GAAG,CAAC8B;QACjE,IAAIzB,MAAM;YACR,OAAOA;QACT;QAEA0E,YAAYzE,GAAG,CACbwB,MACCzB,OAAO+E,MAAMtD,MAAM;YAAEuD,aAAa;QAAc,GAC9C9E,IAAI,CAAC,CAAC+E;YACL,IAAI,CAACA,IAAIC,EAAE,EAAE;gBACX,MAAM,IAAI7C,MAAM,AAAC,gCAA6BZ;YAChD;YACA,OAAOwD,IAAIE,IAAI,GAAGjF,IAAI,CAAC,CAACiF,OAAU,CAAA;oBAAE1D,MAAMA;oBAAM2D,SAASD;gBAAK,CAAA;QAChE,GACC/E,KAAK,CAAC,CAACC;YACN,MAAMI,eAAeJ;QACvB;QAEJ,OAAOL;IACT;IAEA,OAAO;QACLqF,gBAAezB,KAAa;YAC1B,OAAOtE,WAAWsE,OAAOW;QAC3B;QACAe,cAAa1B,KAAa,EAAE2B,OAAoC;YAC5DA,CAAAA,UACE1F,QAAQC,OAAO,GACZI,IAAI,CAAC,IAAMqF,WACXrF,IAAI,CACH,CAACsF,UAAkB,CAAA;oBACjBC,WAAW,AAACD,WAAWA,QAAQE,OAAO,IAAKF;oBAC3CA,SAASA;gBACX,CAAA,GACA,CAACnF,MAAS,CAAA;oBAAEsF,OAAOtF;gBAAI,CAAA,KAE3BR,QAAQC,OAAO,CAAC8F,UAAS,EAC3B1F,IAAI,CAAC,CAAC2F;gBACN,MAAMC,MAAMvB,YAAY5E,GAAG,CAACiE;gBAC5B,IAAIkC,OAAO,aAAaA,KAAK;oBAC3B,IAAID,OAAO;wBACTtB,YAAYtE,GAAG,CAAC2D,OAAOiC;wBACvBC,IAAIhG,OAAO,CAAC+F;oBACd;gBACF,OAAO;oBACL,IAAIA,OAAO;wBACTtB,YAAYtE,GAAG,CAAC2D,OAAOiC;oBACzB,OAAO;wBACLtB,YAAYjE,MAAM,CAACsD;oBACrB;oBACA,gDAAgD;oBAChD,kDAAkD;oBAClD,mBAAmB;oBACnBe,OAAOrE,MAAM,CAACsD;gBAChB;YACF;QACF;QACAmC,WAAUnC,KAAa,EAAEoC,QAAkB;YACzC,OAAO1G,WAA6BsE,OAAOe,QAAQ;gBACjD,IAAIsB;gBAEJ,IAAIjE,QAAQC,GAAG,CAACiB,QAAQ,KAAK,eAAe;oBAC1CN,kBAAkB,IAAI/C,QAAc,CAACC;wBACnCmG,yBAAyBnG;oBAC3B;gBACF;gBAEA,OAAO+C,0BACLa,iBAAiBC,aAAaC,OAC3B1D,IAAI,CAAC;wBAAC,EAAE6D,OAAO,EAAEC,GAAG,EAAE;oBACrB,OAAOnE,QAAQqG,GAAG,CAAC;wBACjB3B,YAAY4B,GAAG,CAACvC,SACZ,EAAE,GACF/D,QAAQqG,GAAG,CAACnC,QAAQvE,GAAG,CAACoF;wBAC5B/E,QAAQqG,GAAG,CAAClC,IAAIxE,GAAG,CAACsF;qBACrB;gBACH,GACC5E,IAAI,CAAC,CAAC+E;oBACL,OAAO,IAAI,CAACI,cAAc,CAACzB,OAAO1D,IAAI,CAAC,CAACkG,aAAgB,CAAA;4BACtDA;4BACAC,QAAQpB,GAAG,CAAC,EAAE;wBAChB,CAAA;gBACF,IACF5F,mBACAoB,eAAe,IAAI4B,MAAM,AAAC,qCAAkCuB,SAE3D1D,IAAI,CAAC;wBAAC,EAAEkG,UAAU,EAAEC,MAAM,EAAE;oBAC3B,MAAMpB,MAAwBvE,OAAO4F,MAAM,CAGzC;wBAAED,QAAQA;oBAAQ,GAAGD;oBACvB,OAAO,WAAWA,aAAaA,aAAanB;gBAC9C,GACC7E,KAAK,CAAC,CAACC;oBACN,IAAI2F,UAAU;wBACZ,gDAAgD;wBAChD,MAAM3F;oBACR;oBACA,OAAO;wBAAEsF,OAAOtF;oBAAI;gBACtB,GACCkG,OAAO,CAAC,IAAMN,0CAAAA;YACnB;QACF;QACAD,UAASpC,KAAa;YACpB,sHAAsH;YACtH,sBAAsB;YACtB,IAAI4C;YACJ,IAAKA,KAAK,AAACC,UAAkBC,UAAU,EAAG;gBACxC,yDAAyD;gBACzD,IAAIF,GAAGG,QAAQ,IAAI,KAAKC,IAAI,CAACJ,GAAGK,aAAa,GAAG,OAAOhH,QAAQC,OAAO;YACxE;YACA,OAAO4D,iBAAiBC,aAAaC,OAClC1D,IAAI,CAAC,CAAC4G,SACLjH,QAAQqG,GAAG,CACT5E,cACIwF,OAAO/C,OAAO,CAACvE,GAAG,CAAC,CAACkD,SAClBlB,eAAekB,OAAOmC,QAAQ,IAAI,aAEpC,EAAE,GAGT3E,IAAI,CAAC;gBACJf,oBAAoB,IAAM,IAAI,CAAC4G,SAAS,CAACnC,OAAO,MAAMxD,KAAK,CAAC,KAAO;YACrE,GACCA,KAAK,CACJ,0BAA0B;YAC1B,KAAO;QAEb;IACF;AACF"}