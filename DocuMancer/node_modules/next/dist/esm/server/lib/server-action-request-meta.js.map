{"version": 3, "sources": ["../../../src/server/lib/server-action-request-meta.ts"], "names": ["ACTION", "getServerActionRequestMetadata", "req", "actionId", "contentType", "headers", "Headers", "get", "toLowerCase", "isURLEncodedAction", "Boolean", "method", "isMultipartAction", "startsWith", "isFetchAction", "undefined", "isServerAction", "getIsServerAction"], "mappings": "AAGA,SAASA,MAAM,QAAQ,6CAA4C;AAEnE,OAAO,SAASC,+BACdC,GAAoD;IAQpD,IAAIC;IACJ,IAAIC;IAEJ,IAAIF,IAAIG,OAAO,YAAYC,SAAS;QAClCH,WAAWD,IAAIG,OAAO,CAACE,GAAG,CAACP,OAAOQ,WAAW,OAAO;QACpDJ,cAAcF,IAAIG,OAAO,CAACE,GAAG,CAAC;IAChC,OAAO;QACLJ,WAAW,AAACD,IAAIG,OAAO,CAACL,OAAOQ,WAAW,GAAG,IAAe;QAC5DJ,cAAcF,IAAIG,OAAO,CAAC,eAAe,IAAI;IAC/C;IAEA,MAAMI,qBAAqBC,QACzBR,IAAIS,MAAM,KAAK,UAAUP,gBAAgB;IAE3C,MAAMQ,oBAAoBF,QACxBR,IAAIS,MAAM,KAAK,WAAUP,+BAAAA,YAAaS,UAAU,CAAC;IAEnD,MAAMC,gBAAgBJ,QACpBP,aAAaY,aACX,OAAOZ,aAAa,YACpBD,IAAIS,MAAM,KAAK;IAGnB,MAAMK,iBAAiBN,QACrBI,iBAAiBL,sBAAsBG;IAGzC,OAAO;QACLT;QACAM;QACAG;QACAE;QACAE;IACF;AACF;AAEA,OAAO,SAASC,kBACdf,GAAoD;IAEpD,OAAOD,+BAA+BC,KAAKc,cAAc;AAC3D"}