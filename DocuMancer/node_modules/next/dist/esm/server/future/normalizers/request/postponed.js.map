{"version": 3, "sources": ["../../../../../src/server/future/normalizers/request/postponed.ts"], "names": ["denormalizePagePath", "PrefixPathnameNormalizer", "prefix", "PostponedPathnameNormalizer", "constructor", "normalize", "pathname", "matched", "match"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,yDAAwD;AAG5F,SAASC,wBAAwB,QAAQ,WAAU;AAEnD,MAAMC,SAAS;AAEf,OAAO,MAAMC,oCACHF;IAGRG,aAAc;QACZ,KAAK,CAACF;IACR;IAEOG,UAAUC,QAAgB,EAAEC,OAAiB,EAAU;QAC5D,uEAAuE;QACvE,IAAI,CAACA,WAAW,CAAC,IAAI,CAACC,KAAK,CAACF,WAAW,OAAOA;QAE9C,qBAAqB;QACrBA,WAAW,KAAK,CAACD,UAAUC,UAAU;QAErC,OAAON,oBAAoBM;IAC7B;AACF"}