{"version": 3, "sources": ["../../../../src/server/api-utils/node/parse-body.ts"], "names": ["parse", "isError", "ApiError", "parseJson", "str", "length", "JSON", "e", "parseBody", "req", "limit", "contentType", "headers", "type", "parameters", "encoding", "charset", "buffer", "getRawBody", "require", "body", "toString", "qs", "decode"], "mappings": "AAGA,SAASA,KAAK,QAAQ,kCAAiC;AACvD,OAAOC,aAAa,wBAAuB;AAC3C,SAASC,QAAQ,QAAQ,WAAU;AAEnC;;;CAGC,GACD,SAASC,UAAUC,GAAW;IAC5B,IAAIA,IAAIC,MAAM,KAAK,GAAG;QACpB,qEAAqE;QACrE,OAAO,CAAC;IACV;IAEA,IAAI;QACF,OAAOC,KAAKN,KAAK,CAACI;IACpB,EAAE,OAAOG,GAAG;QACV,MAAM,IAAIL,SAAS,KAAK;IAC1B;AACF;AAEA;;;CAGC,GACD,OAAO,eAAeM,UACpBC,GAAoB,EACpBC,KAAgB;IAEhB,IAAIC;IACJ,IAAI;QACFA,cAAcX,MAAMS,IAAIG,OAAO,CAAC,eAAe,IAAI;IACrD,EAAE,OAAM;QACND,cAAcX,MAAM;IACtB;IACA,MAAM,EAAEa,IAAI,EAAEC,UAAU,EAAE,GAAGH;IAC7B,MAAMI,WAAWD,WAAWE,OAAO,IAAI;IAEvC,IAAIC;IAEJ,IAAI;QACF,MAAMC,aACJC,QAAQ;QACVF,SAAS,MAAMC,WAAWT,KAAK;YAAEM;YAAUL;QAAM;IACnD,EAAE,OAAOH,GAAG;QACV,IAAIN,QAAQM,MAAMA,EAAEM,IAAI,KAAK,oBAAoB;YAC/C,MAAM,IAAIX,SAAS,KAAK,CAAC,cAAc,EAAEQ,MAAM,MAAM,CAAC;QACxD,OAAO;YACL,MAAM,IAAIR,SAAS,KAAK;QAC1B;IACF;IAEA,MAAMkB,OAAOH,OAAOI,QAAQ;IAE5B,IAAIR,SAAS,sBAAsBA,SAAS,uBAAuB;QACjE,OAAOV,UAAUiB;IACnB,OAAO,IAAIP,SAAS,qCAAqC;QACvD,MAAMS,KAAKH,QAAQ;QACnB,OAAOG,GAAGC,MAAM,CAACH;IACnB,OAAO;QACL,OAAOA;IACT;AACF"}