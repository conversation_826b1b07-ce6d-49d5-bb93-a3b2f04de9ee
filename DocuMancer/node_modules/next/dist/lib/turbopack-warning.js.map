{"version": 3, "sources": ["../../src/lib/turbopack-warning.ts"], "names": ["validateTurboNextConfig", "unsupportedTurbopackNextConfigOptions", "unsupportedProductionSpecificTurbopackNextConfigOptions", "dir", "isDev", "getPkgManager", "require", "getBabelConfigFile", "defaultConfig", "bold", "cyan", "red", "underline", "interopDefault", "unsupportedParts", "babelrc", "path", "basename", "hasWebpackConfig", "hasTurboConfig", "unsupportedConfig", "rawNextConfig", "phase", "PHASE_DEVELOPMENT_SERVER", "PHASE_PRODUCTION_BUILD", "loadConfig", "rawConfig", "flatten<PERSON>eys", "obj", "prefix", "keys", "key", "pre", "length", "Array", "isArray", "concat", "push", "getDeepValue", "split", "slice", "customKeys", "unsupportedKeys", "startsWith", "webpack", "isUnsupported", "some", "unsupported<PERSON>ey", "e", "Log", "error", "feedbackMessage", "warn", "map", "name", "join", "pkgManager", "process", "exit"], "mappings": ";;;;+BA6DsBA;;;eAAAA;;;6DA5DL;+DACM;6DACF;2BAId;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEP,MAAMC,wCAAwC;IAC5C,qBAAqB;IACrB,SAAS;IACT,sBAAsB;IAEtB,oCAAoC;IACpC,qCAAqC;IACrC,yCAAyC;IACzC,sBAAsB;IACtB;IACA,oBAAoB;IACpB;IACA,+BAA+B;IAC/B;IAEA,yBAAyB;IACzB,iCAAiC;IACjC,sCAAsC;IACtC;IACA,qCAAqC;IACrC,mCAAmC;IAEnC;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IAEA,6DAA6D;IAC7D;IACA;IACA;IACA,uEAAuE;IACvE;IACA;IACA;CACD;AAED,kEAAkE;AAClE,MAAMC,0DAA0D;IAC9D;IACA,wEAAwE;IACxE,iCAAiC;IACjC;CACD;AAGM,eAAeF,wBAAwB,EAC5CG,GAAG,EACHC,KAAK,EAIN;IACC,MAAM,EAAEC,aAAa,EAAE,GACrBC,QAAQ;IACV,MAAM,EAAEC,kBAAkB,EAAE,GAC1BD,QAAQ;IACV,MAAM,EAAEE,aAAa,EAAE,GACrBF,QAAQ;IACV,MAAM,EAAEG,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAEC,SAAS,EAAE,GAClCN,QAAQ;IACV,MAAM,EAAEO,cAAc,EAAE,GACtBP,QAAQ;IAEV,IAAIQ,mBAAmB;IACvB,IAAIC,UAAU,MAAMR,mBAAmBJ;IACvC,IAAIY,SAASA,UAAUC,aAAI,CAACC,QAAQ,CAACF;IAErC,IAAIG,mBAAmB;IACvB,IAAIC,iBAAiB;IAErB,IAAIC,oBAA8B,EAAE;IACpC,IAAIC,gBAA4B,CAAC;IAEjC,MAAMC,QAAQlB,QAAQmB,mCAAwB,GAAGC,iCAAsB;IACvE,IAAI;QACFH,gBAAgBR,eACd,MAAMY,IAAAA,eAAU,EAACH,OAAOnB,KAAK;YAC3BuB,WAAW;QACb;QAGF,IAAI,OAAOL,kBAAkB,YAAY;YACvCA,gBAAgB,AAACA,cAAsBC,OAAO;gBAC5Cd;YACF;QACF;QAEA,MAAMmB,cAAc,CAACC,KAAUC,SAAiB,EAAE;YAChD,IAAIC,OAAiB,EAAE;YAEvB,IAAK,MAAMC,OAAOH,IAAK;gBACrB,IAAI,QAAOA,uBAAAA,GAAK,CAACG,IAAI,MAAK,aAAa;oBACrC;gBACF;gBAEA,MAAMC,MAAMH,OAAOI,MAAM,GAAG,CAAC,EAAEJ,OAAO,CAAC,CAAC,GAAG;gBAE3C,IACE,OAAOD,GAAG,CAACG,IAAI,KAAK,YACpB,CAACG,MAAMC,OAAO,CAACP,GAAG,CAACG,IAAI,KACvBH,GAAG,CAACG,IAAI,KAAK,MACb;oBACAD,OAAOA,KAAKM,MAAM,CAACT,YAAYC,GAAG,CAACG,IAAI,EAAEC,MAAMD;gBACjD,OAAO;oBACLD,KAAKO,IAAI,CAACL,MAAMD;gBAClB;YACF;YAEA,OAAOD;QACT;QAEA,MAAMQ,eAAe,CAACV,KAAUE;YAC9B,IAAI,OAAOA,SAAS,UAAU;gBAC5BA,OAAOA,KAAKS,KAAK,CAAC;YACpB;YACA,IAAIT,KAAKG,MAAM,KAAK,GAAG;gBACrB,OAAOL,uBAAAA,GAAK,CAACE,wBAAAA,IAAM,CAAC,EAAE,CAAC;YACzB;YACA,OAAOQ,aAAaV,uBAAAA,GAAK,CAACE,wBAAAA,IAAM,CAAC,EAAE,CAAC,EAAEA,KAAKU,KAAK,CAAC;QACnD;QAEA,MAAMC,aAAad,YAAYN;QAE/B,IAAIqB,kBAAkBtC,QAClBH,wCACA;eACKA;eACAC;SACJ;QAEL,KAAK,MAAM6B,OAAOU,WAAY;YAC5B,IAAIV,IAAIY,UAAU,CAAC,cAActB,cAAcuB,OAAO,EAAE;gBACtD1B,mBAAmB;YACrB;YACA,IAAIa,IAAIY,UAAU,CAAC,uBAAuB;gBACxCxB,iBAAiB;YACnB;YAEA,IAAI0B,gBACFH,gBAAgBI,IAAI,CAClB,CAACC,iBACC,2DAA2D;gBAC3D,+DAA+D;gBAC/D,+BAA+B;gBAC/B,+BAA+B;gBAC/B,+BAA+B;gBAC/B,+BAA+B;gBAC/B,+BAA+B;gBAC/BhB,IAAIY,UAAU,CAACI,mBACfA,eAAeJ,UAAU,CAAC,CAAC,EAAEZ,IAAI,CAAC,CAAC,MAEvCO,aAAajB,eAAeU,SAASO,aAAa9B,eAAeuB;YAEnE,IAAIc,eAAe;gBACjBzB,kBAAkBiB,IAAI,CAACN;YACzB;QACF;IACF,EAAE,OAAOiB,GAAG;QACVC,KAAIC,KAAK,CAAC,mDAAmDF;IAC/D;IAEA,MAAMG,kBAAkB,CAAC,wCAAwC,EAAEvC,UACjE,sCACA,EAAE,CAAC;IAEL,IAAIM,oBAAoB,CAACC,gBAAgB;QACvC8B,KAAIG,IAAI,CACN,CAAC,uEAAuE,CAAC;QAE3EH,KAAIG,IAAI,CACN,CAAC,wHAAwH,CAAC;IAE9H;IAEA,IAAIrC,SAAS;QACXD,oBAAoB,CAAC,gBAAgB,EAAEJ,KACrCK,SACA,8GAA8G,CAAC;IACnH;IAEA,IACEK,kBAAkBa,MAAM,KAAK,KAC7Bb,iBAAiB,CAAC,EAAE,KAAK,uCACzB;QACA6B,KAAIG,IAAI,CACN,CAAC,4FAA4F,CAAC;IAElG,OAAO,IAAIhC,kBAAkBa,MAAM,EAAE;QACnCnB,oBAAoB,CAAC,mDAAmD,EAAEJ,KACxE,kBACA,oEAAoE,EAAEU,kBACrEiC,GAAG,CAAC,CAACC,OAAS,CAAC,MAAM,EAAE3C,IAAI2C,MAAM,EAAE,CAAC,EACpCC,IAAI,CAAC,IAAI,CAAC;IACf;IAEA,IAAIzC,kBAAkB;QACpB,MAAM0C,aAAanD,cAAcF;QAEjC8C,KAAIC,KAAK,CACP,CAAC,iGAAiG,EAAEpC,iBAAiB;;;EAGzH,EAAEL,KACAC,KACE,CAAC,EACC8C,eAAe,QACX,wBACA,CAAC,EAAEA,WAAW,gBAAgB,CAAC,CACpC,4CAA4C,CAAC,GAEhD,6BAA6B,EAAEA,WAAW;QACtC,CAAC;QAGLP,KAAIG,IAAI,CAACD;QAETM,QAAQC,IAAI,CAAC;IACf;IAEA,OAAOrC;AACT"}