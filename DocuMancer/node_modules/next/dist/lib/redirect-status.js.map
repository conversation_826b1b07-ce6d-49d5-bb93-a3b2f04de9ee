{"version": 3, "sources": ["../../src/lib/redirect-status.ts"], "names": ["allowedStatusCodes", "getRedirectStatus", "modifyRouteRegex", "Set", "route", "statusCode", "permanent", "RedirectStatusCode", "PermanentRedirect", "TemporaryRedirect", "regex", "restrictedPaths", "replace", "map", "path", "join"], "mappings": ";;;;;;;;;;;;;;;;IAEaA,kBAAkB;eAAlBA;;IAEGC,iBAAiB;eAAjBA;;IAeAC,gBAAgB;eAAhBA;;;oCAnBmB;AAE5B,MAAMF,qBAAqB,IAAIG,IAAI;IAAC;IAAK;IAAK;IAAK;IAAK;CAAI;AAE5D,SAASF,kBAAkBG,KAGjC;IACC,OACEA,MAAMC,UAAU,IACfD,CAAAA,MAAME,SAAS,GACZC,sCAAkB,CAACC,iBAAiB,GACpCD,sCAAkB,CAACE,iBAAiB,AAAD;AAE3C;AAKO,SAASP,iBAAiBQ,KAAa,EAAEC,eAA0B;IACxE,IAAIA,iBAAiB;QACnBD,QAAQA,MAAME,OAAO,CACnB,MACA,CAAC,IAAI,EAAED,gBACJE,GAAG,CAAC,CAACC,OAASA,KAAKF,OAAO,CAAC,OAAO,QAClCG,IAAI,CAAC,KAAK,CAAC,CAAC;IAEnB;IACAL,QAAQA,MAAME,OAAO,CAAC,OAAO;IAC7B,OAAOF;AACT"}