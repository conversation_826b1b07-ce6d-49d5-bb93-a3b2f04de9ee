{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/prepare-destination.ts"], "names": ["compileNonPath", "matchHas", "prepareDestination", "getSafeParamName", "paramName", "newParamName", "i", "length", "charCode", "charCodeAt", "escapeSegment", "str", "segmentName", "replace", "RegExp", "escapeStringRegexp", "unescapeSegments", "req", "query", "has", "missing", "params", "hasMatch", "hasItem", "value", "key", "type", "toLowerCase", "headers", "cookies", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "host", "hostname", "split", "matcher", "matches", "Array", "isArray", "slice", "match", "groups", "Object", "keys", "for<PERSON>ach", "groupKey", "allMatch", "every", "item", "some", "includes", "compile", "validate", "args", "assign", "__next<PERSON><PERSON><PERSON>", "__nextDefaultLocale", "__nextDataReq", "__nextInferredLocaleFromDefault", "NEXT_RSC_UNION_QUERY", "escapedDestination", "destination", "param", "parsedDestination", "parseUrl", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "destPath", "pathname", "hash", "destHostname", "destPathPara<PERSON><PERSON><PERSON>s", "destHostnameParamKeys", "pathToRegexp", "destParams", "push", "name", "destPathCompiler", "destHostnameCompiler", "strOrArray", "entries", "map", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "appendParamsToQuery", "newUrl", "isInterceptionRouteAppPath", "segment", "marker", "INTERCEPTION_ROUTE_MARKERS", "find", "m", "startsWith", "search", "err", "message", "Error"], "mappings": ";;;;;;;;;;;;;;;;IA+HgBA,cAAc;eAAdA;;IA/EAC,QAAQ;eAARA;;IAkHAC,kBAAkB;eAAlBA;;;8BA3JsB;8BACH;0BACV;oCAIlB;kCAC8B;iCACL;AAEhC;;;CAGC,GACD,SAASC,iBAAiBC,SAAiB;IACzC,IAAIC,eAAe;IAEnB,IAAK,IAAIC,IAAI,GAAGA,IAAIF,UAAUG,MAAM,EAAED,IAAK;QACzC,MAAME,WAAWJ,UAAUK,UAAU,CAACH;QAEtC,IACE,AAACE,WAAW,MAAMA,WAAW,MAAO,MAAM;QACzCA,WAAW,MAAMA,WAAW,IAAK,MAAM;UACxC;YACAH,gBAAgBD,SAAS,CAACE,EAAE;QAC9B;IACF;IACA,OAAOD;AACT;AAEA,SAASK,cAAcC,GAAW,EAAEC,WAAmB;IACrD,OAAOD,IAAIE,OAAO,CAChB,IAAIC,OAAO,AAAC,MAAGC,IAAAA,gCAAkB,EAACH,cAAgB,MAClD,AAAC,iBAAcA;AAEnB;AAEA,SAASI,iBAAiBL,GAAW;IACnC,OAAOA,IAAIE,OAAO,CAAC,kBAAkB;AACvC;AAEO,SAASZ,SACdgB,GAAsC,EACtCC,KAAa,EACbC,GAAoB,EACpBC,OAAwB;IADxBD,IAAAA,gBAAAA,MAAkB,EAAE;IACpBC,IAAAA,oBAAAA,UAAsB,EAAE;IAExB,MAAMC,SAAiB,CAAC;IAExB,MAAMC,WAAW,CAACC;QAChB,IAAIC;QACJ,IAAIC,MAAMF,QAAQE,GAAG;QAErB,OAAQF,QAAQG,IAAI;YAClB,KAAK;gBAAU;oBACbD,MAAMA,IAAKE,WAAW;oBACtBH,QAAQP,IAAIW,OAAO,CAACH,IAAI;oBACxB;gBACF;YACA,KAAK;gBAAU;oBACb,IAAI,aAAaR,KAAK;wBACpBO,QAAQP,IAAIY,OAAO,CAACN,QAAQE,GAAG,CAAC;oBAClC,OAAO;wBACL,MAAMI,UAAUC,IAAAA,gCAAe,EAACb,IAAIW,OAAO;wBAC3CJ,QAAQK,OAAO,CAACN,QAAQE,GAAG,CAAC;oBAC9B;oBAEA;gBACF;YACA,KAAK;gBAAS;oBACZD,QAAQN,KAAK,CAACO,IAAK;oBACnB;gBACF;YACA,KAAK;gBAAQ;oBACX,MAAM,EAAEM,IAAI,EAAE,GAAGd,CAAAA,uBAAAA,IAAKW,OAAO,KAAI,CAAC;oBAClC,mCAAmC;oBACnC,MAAMI,WAAWD,wBAAAA,KAAME,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,CAACN,WAAW;oBACnDH,QAAQQ;oBACR;gBACF;YACA;gBAAS;oBACP;gBACF;QACF;QAEA,IAAI,CAACT,QAAQC,KAAK,IAAIA,OAAO;YAC3BH,MAAM,CAAClB,iBAAiBsB,KAAM,GAAGD;YACjC,OAAO;QACT,OAAO,IAAIA,OAAO;YAChB,MAAMU,UAAU,IAAIpB,OAAO,AAAC,MAAGS,QAAQC,KAAK,GAAC;YAC7C,MAAMW,UAAUC,MAAMC,OAAO,CAACb,SAC1BA,MAAMc,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAACC,KAAK,CAACL,WACzBV,MAAMe,KAAK,CAACL;YAEhB,IAAIC,SAAS;gBACX,IAAIC,MAAMC,OAAO,CAACF,UAAU;oBAC1B,IAAIA,QAAQK,MAAM,EAAE;wBAClBC,OAAOC,IAAI,CAACP,QAAQK,MAAM,EAAEG,OAAO,CAAC,CAACC;4BACnCvB,MAAM,CAACuB,SAAS,GAAGT,QAAQK,MAAM,AAAC,CAACI,SAAS;wBAC9C;oBACF,OAAO,IAAIrB,QAAQG,IAAI,KAAK,UAAUS,OAAO,CAAC,EAAE,EAAE;wBAChDd,OAAOU,IAAI,GAAGI,OAAO,CAAC,EAAE;oBAC1B;gBACF;gBACA,OAAO;YACT;QACF;QACA,OAAO;IACT;IAEA,MAAMU,WACJ1B,IAAI2B,KAAK,CAAC,CAACC,OAASzB,SAASyB,UAC7B,CAAC3B,QAAQ4B,IAAI,CAAC,CAACD,OAASzB,SAASyB;IAEnC,IAAIF,UAAU;QACZ,OAAOxB;IACT;IACA,OAAO;AACT;AAEO,SAASrB,eAAewB,KAAa,EAAEH,MAAc;IAC1D,IAAI,CAACG,MAAMyB,QAAQ,CAAC,MAAM;QACxB,OAAOzB;IACT;IAEA,KAAK,MAAMC,OAAOgB,OAAOC,IAAI,CAACrB,QAAS;QACrC,IAAIG,MAAMyB,QAAQ,CAAC,AAAC,MAAGxB,MAAQ;YAC7BD,QAAQA,MACLX,OAAO,CACN,IAAIC,OAAO,AAAC,MAAGW,MAAI,OAAM,MACzB,AAAC,MAAGA,MAAI,6BAETZ,OAAO,CACN,IAAIC,OAAO,AAAC,MAAGW,MAAI,OAAM,MACzB,AAAC,MAAGA,MAAI,4BAETZ,OAAO,CAAC,IAAIC,OAAO,AAAC,MAAGW,MAAI,OAAM,MAAM,AAAC,MAAGA,MAAI,wBAC/CZ,OAAO,CACN,IAAIC,OAAO,AAAC,MAAGW,MAAI,WAAU,MAC7B,AAAC,0BAAuBA;QAE9B;IACF;IACAD,QAAQA,MACLX,OAAO,CAAC,6BAA6B,QACrCA,OAAO,CAAC,yBAAyB,KACjCA,OAAO,CAAC,0BAA0B,KAClCA,OAAO,CAAC,6BAA6B,KACrCA,OAAO,CAAC,8BAA8B;IAEzC,+DAA+D;IAC/D,YAAY;IACZ,OAAOqC,IAAAA,qBAAO,EAAC,AAAC,MAAG1B,OAAS;QAAE2B,UAAU;IAAM,GAAG9B,QAAQiB,KAAK,CAAC;AACjE;AAEO,SAASpC,mBAAmBkD,IAKlC;IACC,MAAMlC,QAAQuB,OAAOY,MAAM,CAAC,CAAC,GAAGD,KAAKlC,KAAK;IAC1C,OAAOA,MAAMoC,YAAY;IACzB,OAAOpC,MAAMqC,mBAAmB;IAChC,OAAOrC,MAAMsC,aAAa;IAC1B,OAAOtC,MAAMuC,+BAA+B;IAC5C,OAAOvC,KAAK,CAACwC,sCAAoB,CAAC;IAElC,IAAIC,qBAAqBP,KAAKQ,WAAW;IAEzC,KAAK,MAAMC,SAASpB,OAAOC,IAAI,CAAC;QAAE,GAAGU,KAAK/B,MAAM;QAAE,GAAGH,KAAK;IAAC,GAAI;QAC7DyC,qBAAqBjD,cAAciD,oBAAoBE;IACzD;IAEA,MAAMC,oBAAoBC,IAAAA,kBAAQ,EAACJ;IACnC,MAAMK,YAAYF,kBAAkB5C,KAAK;IACzC,MAAM+C,WAAWjD,iBACf,AAAC,KAAE8C,kBAAkBI,QAAQ,GAAIJ,CAAAA,kBAAkBK,IAAI,IAAI,EAAC;IAE9D,MAAMC,eAAepD,iBAAiB8C,kBAAkB9B,QAAQ,IAAI;IACpE,MAAMqC,oBAA2B,EAAE;IACnC,MAAMC,wBAA+B,EAAE;IACvCC,IAAAA,0BAAY,EAACN,UAAUI;IACvBE,IAAAA,0BAAY,EAACH,cAAcE;IAE3B,MAAME,aAAkC,EAAE;IAE1CH,kBAAkB1B,OAAO,CAAC,CAAClB,MAAQ+C,WAAWC,IAAI,CAAChD,IAAIiD,IAAI;IAC3DJ,sBAAsB3B,OAAO,CAAC,CAAClB,MAAQ+C,WAAWC,IAAI,CAAChD,IAAIiD,IAAI;IAE/D,MAAMC,mBAAmBzB,IAAAA,qBAAO,EAC9Be,UACA,oEAAoE;IACpE,oEAAoE;IACpE,0EAA0E;IAC1E,yEAAyE;IACzE,wEAAwE;IACxE,iDAAiD;IACjD;QAAEd,UAAU;IAAM;IAGpB,MAAMyB,uBAAuB1B,IAAAA,qBAAO,EAACkB,cAAc;QAAEjB,UAAU;IAAM;IAErE,oCAAoC;IACpC,KAAK,MAAM,CAAC1B,KAAKoD,WAAW,IAAIpC,OAAOqC,OAAO,CAACd,WAAY;QACzD,+DAA+D;QAC/D,YAAY;QACZ,IAAI5B,MAAMC,OAAO,CAACwC,aAAa;YAC7Bb,SAAS,CAACvC,IAAI,GAAGoD,WAAWE,GAAG,CAAC,CAACvD,QAC/BxB,eAAegB,iBAAiBQ,QAAQ4B,KAAK/B,MAAM;QAEvD,OAAO,IAAI,OAAOwD,eAAe,UAAU;YACzCb,SAAS,CAACvC,IAAI,GAAGzB,eAAegB,iBAAiB6D,aAAazB,KAAK/B,MAAM;QAC3E;IACF;IAEA,0DAA0D;IAC1D,+CAA+C;IAC/C,IAAI2D,YAAYvC,OAAOC,IAAI,CAACU,KAAK/B,MAAM,EAAE4D,MAAM,CAC7C,CAACP,OAASA,SAAS;IAGrB,IACEtB,KAAK8B,mBAAmB,IACxB,CAACF,UAAUhC,IAAI,CAAC,CAACvB,MAAQ+C,WAAWvB,QAAQ,CAACxB,OAC7C;QACA,KAAK,MAAMA,OAAOuD,UAAW;YAC3B,IAAI,CAAEvD,CAAAA,OAAOuC,SAAQ,GAAI;gBACvBA,SAAS,CAACvC,IAAI,GAAG2B,KAAK/B,MAAM,CAACI,IAAI;YACnC;QACF;IACF;IAEA,IAAI0D;IAEJ,uFAAuF;IACvF,6CAA6C;IAC7C,IAAIC,IAAAA,8CAA0B,EAACnB,WAAW;QACxC,KAAK,MAAMoB,WAAWpB,SAAShC,KAAK,CAAC,KAAM;YACzC,MAAMqD,SAASC,8CAA0B,CAACC,IAAI,CAAC,CAACC,IAC9CJ,QAAQK,UAAU,CAACD;YAErB,IAAIH,QAAQ;gBACVlC,KAAK/B,MAAM,CAAC,IAAI,GAAGiE;gBACnB;YACF;QACF;IACF;IAEA,IAAI;QACFH,SAASR,iBAAiBvB,KAAK/B,MAAM;QAErC,MAAM,CAAC6C,UAAUC,KAAK,GAAGgB,OAAOlD,KAAK,CAAC,KAAK;QAC3C6B,kBAAkB9B,QAAQ,GAAG4C,qBAAqBxB,KAAK/B,MAAM;QAC7DyC,kBAAkBI,QAAQ,GAAGA;QAC7BJ,kBAAkBK,IAAI,GAAG,AAAC,KAAEA,CAAAA,OAAO,MAAM,EAAC,IAAIA,CAAAA,QAAQ,EAAC;QACvD,OAAO,AAACL,kBAA0B6B,MAAM;IAC1C,EAAE,OAAOC,KAAU;QACjB,IAAIA,IAAIC,OAAO,CAACtD,KAAK,CAAC,iDAAiD;YACrE,MAAM,IAAIuD,MACP;QAEL;QACA,MAAMF;IACR;IAEA,+CAA+C;IAC/C,8BAA8B;IAC9B,yBAAyB;IACzB,wCAAwC;IACxC9B,kBAAkB5C,KAAK,GAAG;QACxB,GAAGA,KAAK;QACR,GAAG4C,kBAAkB5C,KAAK;IAC5B;IAEA,OAAO;QACLiE;QACAnB;QACAF;IACF;AACF"}