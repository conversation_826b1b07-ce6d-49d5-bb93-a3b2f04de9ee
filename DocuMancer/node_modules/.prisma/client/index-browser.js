
Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 5.22.0
 * Query Engine version: 605197351a3c8bdd595af2d2a9bc3025bca48ea2
 */
Prisma.prismaVersion = {
  client: "5.22.0",
  engine: "605197351a3c8bdd595af2d2a9bc3025bca48ea2"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.NotFoundError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`NotFoundError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  email: 'email',
  name: 'name',
  password: 'password',
  avatar: 'avatar',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PaperScalarFieldEnum = {
  id: 'id',
  title: 'title',
  authors: 'authors',
  abstract: 'abstract',
  keywords: 'keywords',
  doi: 'doi',
  journal: 'journal',
  year: 'year',
  pages: 'pages',
  url: 'url',
  filePath: 'filePath',
  fileName: 'fileName',
  fileSize: 'fileSize',
  mimeType: 'mimeType',
  uploadedAt: 'uploadedAt',
  processedAt: 'processedAt',
  status: 'status',
  userId: 'userId'
};

exports.Prisma.ChunkScalarFieldEnum = {
  id: 'id',
  content: 'content',
  chunkIndex: 'chunkIndex',
  startPage: 'startPage',
  endPage: 'endPage',
  embeddingId: 'embeddingId',
  metadata: 'metadata',
  paperId: 'paperId'
};

exports.Prisma.ConversationScalarFieldEnum = {
  id: 'id',
  title: 'title',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId',
  paperId: 'paperId'
};

exports.Prisma.MessageScalarFieldEnum = {
  id: 'id',
  content: 'content',
  role: 'role',
  metadata: 'metadata',
  createdAt: 'createdAt',
  conversationId: 'conversationId'
};

exports.Prisma.AnnotationScalarFieldEnum = {
  id: 'id',
  content: 'content',
  position: 'position',
  type: 'type',
  color: 'color',
  createdAt: 'createdAt',
  userId: 'userId',
  paperId: 'paperId'
};

exports.Prisma.SummaryScalarFieldEnum = {
  id: 'id',
  background: 'background',
  methodology: 'methodology',
  results: 'results',
  conclusions: 'conclusions',
  keyFindings: 'keyFindings',
  limitations: 'limitations',
  type: 'type',
  createdAt: 'createdAt',
  paperId: 'paperId'
};

exports.Prisma.ExtractionScalarFieldEnum = {
  id: 'id',
  researchType: 'researchType',
  methodology: 'methodology',
  datasets: 'datasets',
  metrics: 'metrics',
  tools: 'tools',
  contributions: 'contributions',
  createdAt: 'createdAt',
  paperId: 'paperId'
};

exports.Prisma.ComparisonScalarFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  analysis: 'analysis',
  createdAt: 'createdAt'
};

exports.Prisma.ComparisonPaperScalarFieldEnum = {
  id: 'id',
  comparisonId: 'comparisonId',
  paperId: 'paperId'
};

exports.Prisma.CollectionScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  isPublic: 'isPublic',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId'
};

exports.Prisma.CollectionPaperScalarFieldEnum = {
  id: 'id',
  addedAt: 'addedAt',
  notes: 'notes',
  collectionId: 'collectionId',
  paperId: 'paperId'
};

exports.Prisma.PaperShareScalarFieldEnum = {
  id: 'id',
  permissions: 'permissions',
  expiresAt: 'expiresAt',
  createdAt: 'createdAt',
  paperId: 'paperId',
  userId: 'userId'
};

exports.Prisma.CitationScalarFieldEnum = {
  id: 'id',
  context: 'context',
  createdAt: 'createdAt',
  paperId: 'paperId',
  citedPaperId: 'citedPaperId'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};


exports.Prisma.ModelName = {
  User: 'User',
  Paper: 'Paper',
  Chunk: 'Chunk',
  Conversation: 'Conversation',
  Message: 'Message',
  Annotation: 'Annotation',
  Summary: 'Summary',
  Extraction: 'Extraction',
  Comparison: 'Comparison',
  ComparisonPaper: 'ComparisonPaper',
  Collection: 'Collection',
  CollectionPaper: 'CollectionPaper',
  PaperShare: 'PaperShare',
  Citation: 'Citation'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }
        
        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
