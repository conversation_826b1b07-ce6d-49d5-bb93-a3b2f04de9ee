generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  password  String
  avatar    String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  papers        Paper[]
  conversations Conversation[]
  annotations   Annotation[]
  collections   Collection[]
  shares        PaperShare[]

  @@map("users")
}

model Paper {
  id          String    @id @default(cuid())
  title       String
  authors     String // JSON string for array
  abstract    String?
  keywords    String // JSON string for array
  doi         String?
  journal     String?
  year        Int?
  pages       String?
  url         String?
  filePath    String
  fileName    String
  fileSize    Int
  mimeType    String
  uploadedAt  DateTime  @default(now())
  processedAt DateTime?
  status      String    @default("PROCESSING") // PROCESSING, COMPLETED, FAILED

  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  chunks        Chunk[]
  conversations Conversation[]
  annotations   Annotation[]
  summaries     Summary[]
  extractions   Extraction[]
  comparisons   ComparisonPaper[]
  collections   CollectionPaper[]
  shares        PaperShare[]
  citations     Citation[]
  citedBy       Citation[]        @relation("CitedPaper")

  @@map("papers")
}

model Chunk {
  id          String  @id @default(cuid())
  content     String
  chunkIndex  Int
  startPage   Int?
  endPage     Int?
  embeddingId String?
  metadata    String? // JSON string

  paperId String
  paper   Paper  @relation(fields: [paperId], references: [id], onDelete: Cascade)

  @@map("chunks")
}

model Conversation {
  id        String   @id @default(cuid())
  title     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  userId  String
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  paperId String?
  paper   Paper?  @relation(fields: [paperId], references: [id], onDelete: Cascade)

  messages Message[]

  @@map("conversations")
}

model Message {
  id        String   @id @default(cuid())
  content   String
  role      String // USER, ASSISTANT, SYSTEM
  metadata  String? // JSON string
  createdAt DateTime @default(now())

  conversationId String
  conversation   Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)

  @@map("messages")
}

model Annotation {
  id        String   @id @default(cuid())
  content   String
  position  String // JSON string
  type      String   @default("HIGHLIGHT") // HIGHLIGHT, NOTE, BOOKMARK
  color     String?
  createdAt DateTime @default(now())

  userId  String
  user    User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  paperId String
  paper   Paper  @relation(fields: [paperId], references: [id], onDelete: Cascade)

  @@map("annotations")
}

model Summary {
  id          String   @id @default(cuid())
  background  String?
  methodology String?
  results     String?
  conclusions String?
  keyFindings String // JSON string for array
  limitations String?
  type        String   @default("STRUCTURED") // STRUCTURED, ABSTRACT, EXECUTIVE
  createdAt   DateTime @default(now())

  paperId String @unique
  paper   Paper  @relation(fields: [paperId], references: [id], onDelete: Cascade)

  @@map("summaries")
}

model Extraction {
  id            String   @id @default(cuid())
  researchType  String?
  methodology   String?
  datasets      String // JSON string for array
  metrics       String // JSON string for array
  tools         String // JSON string for array
  contributions String // JSON string for array
  createdAt     DateTime @default(now())

  paperId String @unique
  paper   Paper  @relation(fields: [paperId], references: [id], onDelete: Cascade)

  @@map("extractions")
}

model Comparison {
  id          String   @id @default(cuid())
  title       String
  description String?
  analysis    String // JSON string
  createdAt   DateTime @default(now())

  papers ComparisonPaper[]

  @@map("comparisons")
}

model ComparisonPaper {
  id String @id @default(cuid())

  comparisonId String
  comparison   Comparison @relation(fields: [comparisonId], references: [id], onDelete: Cascade)
  paperId      String
  paper        Paper      @relation(fields: [paperId], references: [id], onDelete: Cascade)

  @@unique([comparisonId, paperId])
  @@map("comparison_papers")
}

model Collection {
  id          String   @id @default(cuid())
  name        String
  description String?
  isPublic    Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  papers CollectionPaper[]

  @@map("collections")
}

model CollectionPaper {
  id      String   @id @default(cuid())
  addedAt DateTime @default(now())
  notes   String?

  collectionId String
  collection   Collection @relation(fields: [collectionId], references: [id], onDelete: Cascade)
  paperId      String
  paper        Paper      @relation(fields: [paperId], references: [id], onDelete: Cascade)

  @@unique([collectionId, paperId])
  @@map("collection_papers")
}

model PaperShare {
  id          String    @id @default(cuid())
  permissions String    @default("READ") // READ, COMMENT, EDIT
  expiresAt   DateTime?
  createdAt   DateTime  @default(now())

  paperId String
  paper   Paper  @relation(fields: [paperId], references: [id], onDelete: Cascade)
  userId  String
  user    User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([paperId, userId])
  @@map("paper_shares")
}

model Citation {
  id        String   @id @default(cuid())
  context   String?
  createdAt DateTime @default(now())

  paperId      String
  paper        Paper  @relation(fields: [paperId], references: [id], onDelete: Cascade)
  citedPaperId String
  citedPaper   Paper  @relation("CitedPaper", fields: [citedPaperId], references: [id], onDelete: Cascade)

  @@unique([paperId, citedPaperId])
  @@map("citations")
}

// Enums are replaced with String fields with comments indicating valid values
// PaperStatus: PROCESSING, COMPLETED, FAILED
// MessageRole: USER, ASSISTANT, SYSTEM
// AnnotationType: HIGHLIGHT, NOTE, BOOKMARK
// SummaryType: STRUCTURED, ABSTRACT, EXECUTIVE
// SharePermission: READ, COMMENT, EDIT
