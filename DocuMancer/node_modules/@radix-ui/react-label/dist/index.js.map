{"version": 3, "sources": ["../src/index.ts", "../src/label.tsx"], "sourcesContent": ["'use client';\nexport {\n  Label,\n  //\n  Root,\n} from './label';\nexport type { LabelProps } from './label';\n", "import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * Label\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'Label';\n\ntype LabelElement = React.ComponentRef<typeof Primitive.label>;\ntype PrimitiveLabelProps = React.ComponentPropsWithoutRef<typeof Primitive.label>;\ninterface LabelProps extends PrimitiveLabelProps {}\n\nconst Label = React.forwardRef<LabelElement, LabelProps>((props, forwardedRef) => {\n  return (\n    <Primitive.label\n      {...props}\n      ref={forwardedRef}\n      onMouseDown={(event) => {\n        // only prevent text selection if clicking inside the label itself\n        const target = event.target as HTMLElement;\n        if (target.closest('button, input, select, textarea')) return;\n\n        props.onMouseDown?.(event);\n        // prevent text selection when double clicking label\n        if (!event.defaultPrevented && event.detail > 1) event.preventDefault();\n      }}\n    />\n  );\n});\n\nLabel.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Label;\n\nexport {\n  Label,\n  //\n  Root,\n};\nexport type { LabelProps };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,YAAuB;AACvB,6BAA0B;AActB;AARJ,IAAM,OAAO;AAMb,IAAM,QAAc,iBAAqC,CAAC,OAAO,iBAAiB;AAChF,SACE;AAAA,IAAC,iCAAU;AAAA,IAAV;AAAA,MACE,GAAG;AAAA,MACJ,KAAK;AAAA,MACL,aAAa,CAAC,UAAU;AAEtB,cAAM,SAAS,MAAM;AACrB,YAAI,OAAO,QAAQ,iCAAiC,EAAG;AAEvD,cAAM,cAAc,KAAK;AAEzB,YAAI,CAAC,MAAM,oBAAoB,MAAM,SAAS,EAAG,OAAM,eAAe;AAAA,MACxE;AAAA;AAAA,EACF;AAEJ,CAAC;AAED,MAAM,cAAc;AAIpB,IAAM,OAAO;", "names": []}