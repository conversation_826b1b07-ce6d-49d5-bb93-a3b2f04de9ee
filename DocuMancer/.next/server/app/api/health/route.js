"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/health/route";
exports.ids = ["app/api/health/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "worker_threads":
/*!*********************************!*\
  !*** external "worker_threads" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("worker_threads");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:crypto");

/***/ }),

/***/ "node:fs":
/*!**************************!*\
  !*** external "node:fs" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("node:fs");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:stream");

/***/ }),

/***/ "node:stream/web":
/*!**********************************!*\
  !*** external "node:stream/web" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("node:stream/web");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fhealth%2Froute&page=%2Fapi%2Fhealth%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fhealth%2Froute.ts&appDir=%2Fhome%2Fbwy%2Fproject%2FDocuMancer%2FDocuMancer%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fbwy%2Fproject%2FDocuMancer%2FDocuMancer&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fhealth%2Froute&page=%2Fapi%2Fhealth%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fhealth%2Froute.ts&appDir=%2Fhome%2Fbwy%2Fproject%2FDocuMancer%2FDocuMancer%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fbwy%2Fproject%2FDocuMancer%2FDocuMancer&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_bwy_project_DocuMancer_DocuMancer_src_app_api_health_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/health/route.ts */ \"(rsc)/./src/app/api/health/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/health/route\",\n        pathname: \"/api/health\",\n        filename: \"route\",\n        bundlePath: \"app/api/health/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/project/DocuMancer/DocuMancer/src/app/api/health/route.ts\",\n    nextConfigOutput,\n    userland: _home_bwy_project_DocuMancer_DocuMancer_src_app_api_health_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/health/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fhealth%2Froute&page=%2Fapi%2Fhealth%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fhealth%2Froute.ts&appDir=%2Fhome%2Fbwy%2Fproject%2FDocuMancer%2FDocuMancer%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fbwy%2Fproject%2FDocuMancer%2FDocuMancer&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/health/route.ts":
/*!*************************************!*\
  !*** ./src/app/api/health/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n\n\nasync function GET() {\n    try {\n        const startTime = Date.now();\n        // Check database health\n        const dbHealth = await (0,_lib_db__WEBPACK_IMPORTED_MODULE_1__.checkDBHealth)();\n        // Check environment variables\n        const requiredEnvVars = [\n            \"DATABASE_URL\",\n            \"NEXTAUTH_SECRET\",\n            \"OPENAI_API_KEY\"\n        ];\n        const missingEnvVars = requiredEnvVars.filter((envVar)=>!process.env[envVar]);\n        // Check vector database connectivity\n        let vectorDbHealth = {\n            status: \"healthy\"\n        };\n        try {\n            if (process.env.PINECONE_API_KEY) {\n                // Test Pinecone connection\n                const { Pinecone } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/node-fetch\"), __webpack_require__.e(\"vendor-chunks/@pinecone-database\"), __webpack_require__.e(\"vendor-chunks/ajv\"), __webpack_require__.e(\"vendor-chunks/fast-uri\"), __webpack_require__.e(\"vendor-chunks/cross-fetch\"), __webpack_require__.e(\"vendor-chunks/json-schema-traverse\"), __webpack_require__.e(\"vendor-chunks/fast-deep-equal\"), __webpack_require__.e(\"vendor-chunks/@sinclair\")]).then(__webpack_require__.t.bind(__webpack_require__, /*! @pinecone-database/pinecone */ \"(rsc)/./node_modules/@pinecone-database/pinecone/dist/index.js\", 23));\n                const pinecone = new Pinecone({\n                    apiKey: process.env.PINECONE_API_KEY,\n                    environment: process.env.PINECONE_ENVIRONMENT || \"\"\n                });\n                await pinecone.listIndexes();\n            } else if (process.env.CHROMA_URL) {\n                // Test ChromaDB connection\n                const response = await fetch(`${process.env.CHROMA_URL}/api/v1/heartbeat`);\n                if (!response.ok) {\n                    throw new Error(\"ChromaDB not responding\");\n                }\n            }\n        } catch (error) {\n            vectorDbHealth = {\n                status: \"unhealthy\",\n                error: error.message\n            };\n        }\n        // Check LLM connectivity\n        let llmHealth = {\n            status: \"healthy\"\n        };\n        try {\n            if (process.env.OPENAI_API_KEY) {\n                const { ChatOpenAI } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/node-fetch\"), __webpack_require__.e(\"vendor-chunks/formdata-node\"), __webpack_require__.e(\"vendor-chunks/@langchain\"), __webpack_require__.e(\"vendor-chunks/openai\"), __webpack_require__.e(\"vendor-chunks/semver\"), __webpack_require__.e(\"vendor-chunks/zod-to-json-schema\"), __webpack_require__.e(\"vendor-chunks/langsmith\"), __webpack_require__.e(\"vendor-chunks/form-data-encoder\"), __webpack_require__.e(\"vendor-chunks/zod\"), __webpack_require__.e(\"vendor-chunks/uuid\"), __webpack_require__.e(\"vendor-chunks/p-queue\"), __webpack_require__.e(\"vendor-chunks/agentkeepalive\"), __webpack_require__.e(\"vendor-chunks/retry\"), __webpack_require__.e(\"vendor-chunks/js-tiktoken\"), __webpack_require__.e(\"vendor-chunks/p-timeout\"), __webpack_require__.e(\"vendor-chunks/p-retry\"), __webpack_require__.e(\"vendor-chunks/p-finally\"), __webpack_require__.e(\"vendor-chunks/ms\"), __webpack_require__.e(\"vendor-chunks/humanize-ms\"), __webpack_require__.e(\"vendor-chunks/event-target-shim\"), __webpack_require__.e(\"vendor-chunks/decamelize\"), __webpack_require__.e(\"vendor-chunks/camelcase\"), __webpack_require__.e(\"vendor-chunks/base64-js\"), __webpack_require__.e(\"vendor-chunks/ansi-styles\"), __webpack_require__.e(\"vendor-chunks/abort-controller\")]).then(__webpack_require__.bind(__webpack_require__, /*! @langchain/openai */ \"(rsc)/./node_modules/@langchain/openai/index.js\"));\n                const llm = new ChatOpenAI({\n                    openAIApiKey: process.env.OPENAI_API_KEY,\n                    modelName: \"gpt-3.5-turbo\",\n                    maxTokens: 1\n                });\n                await llm.invoke(\"test\");\n            }\n        } catch (error) {\n            llmHealth = {\n                status: \"unhealthy\",\n                error: error.message\n            };\n        }\n        const responseTime = Date.now() - startTime;\n        const health = {\n            status: \"healthy\",\n            timestamp: new Date().toISOString(),\n            uptime: process.uptime(),\n            responseTime,\n            version: process.env.npm_package_version || \"1.0.0\",\n            environment: \"development\" || 0,\n            services: {\n                database: dbHealth,\n                vectorDatabase: vectorDbHealth,\n                llm: llmHealth\n            },\n            system: {\n                nodeVersion: process.version,\n                platform: process.platform,\n                memory: {\n                    used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),\n                    total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024)\n                }\n            },\n            configuration: {\n                missingEnvVars,\n                hasOpenAI: !!process.env.OPENAI_API_KEY,\n                hasAnthropic: !!process.env.ANTHROPIC_API_KEY,\n                hasPinecone: !!process.env.PINECONE_API_KEY,\n                hasChroma: !!process.env.CHROMA_URL,\n                hasLangSmith: !!process.env.LANGCHAIN_API_KEY\n            }\n        };\n        // Determine overall health status\n        const isHealthy = dbHealth.status === \"healthy\" && vectorDbHealth.status === \"healthy\" && llmHealth.status === \"healthy\" && missingEnvVars.length === 0;\n        if (!isHealthy) {\n            health.status = \"unhealthy\";\n        }\n        const statusCode = isHealthy ? 200 : 503;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(health, {\n            status: statusCode\n        });\n    } catch (error) {\n        console.error(\"Health check error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            status: \"unhealthy\",\n            timestamp: new Date().toISOString(),\n            error: error.message\n        }, {\n            status: 503\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/health/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkDBHealth: () => (/* binding */ checkDBHealth),\n/* harmony export */   connectDB: () => (/* binding */ connectDB),\n/* harmony export */   disconnectDB: () => (/* binding */ disconnectDB),\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n// Database utility functions\nconst connectDB = async ()=>{\n    try {\n        await prisma.$connect();\n        console.log(\"Database connected successfully\");\n    } catch (error) {\n        console.error(\"Database connection failed:\", error);\n        throw error;\n    }\n};\nconst disconnectDB = async ()=>{\n    await prisma.$disconnect();\n};\n// Health check\nconst checkDBHealth = async ()=>{\n    try {\n        await prisma.$queryRaw`SELECT 1`;\n        return {\n            status: \"healthy\",\n            timestamp: new Date().toISOString()\n        };\n    } catch (error) {\n        return {\n            status: \"unhealthy\",\n            error: error.message,\n            timestamp: new Date().toISOString()\n        };\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fhealth%2Froute&page=%2Fapi%2Fhealth%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fhealth%2Froute.ts&appDir=%2Fhome%2Fbwy%2Fproject%2FDocuMancer%2FDocuMancer%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fbwy%2Fproject%2FDocuMancer%2FDocuMancer&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();