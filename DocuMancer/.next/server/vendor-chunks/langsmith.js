"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/langsmith";
exports.ids = ["vendor-chunks/langsmith"];
exports.modules = {

/***/ "(rsc)/./node_modules/langsmith/node_modules/uuid/dist/esm-node/native.js":
/*!**************************************************************************!*\
  !*** ./node_modules/langsmith/node_modules/uuid/dist/esm-node/native.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(node_crypto__WEBPACK_IMPORTED_MODULE_0__);\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n  randomUUID: (node_crypto__WEBPACK_IMPORTED_MODULE_0___default().randomUUID)\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbGFuZ3NtaXRoL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLW5vZGUvbmF0aXZlLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFpQztBQUNqQyxpRUFBZTtBQUNmLGNBQWMsK0RBQWlCO0FBQy9CLENBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1wYXBlci1yZWFkaW5nLWFzc2lzdGFudC8uL25vZGVfbW9kdWxlcy9sYW5nc21pdGgvbm9kZV9tb2R1bGVzL3V1aWQvZGlzdC9lc20tbm9kZS9uYXRpdmUuanM/MmZiNCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3J5cHRvIGZyb20gJ25vZGU6Y3J5cHRvJztcbmV4cG9ydCBkZWZhdWx0IHtcbiAgcmFuZG9tVVVJRDogY3J5cHRvLnJhbmRvbVVVSURcbn07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/langsmith/node_modules/uuid/dist/esm-node/native.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/langsmith/node_modules/uuid/dist/esm-node/regex.js":
/*!*************************************************************************!*\
  !*** ./node_modules/langsmith/node_modules/uuid/dist/esm-node/regex.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-8][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/i);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbGFuZ3NtaXRoL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLW5vZGUvcmVnZXguanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWMsRUFBRSxVQUFVLEVBQUUsZUFBZSxFQUFFLGdCQUFnQixFQUFFLFVBQVUsR0FBRyw4RUFBOEUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1wYXBlci1yZWFkaW5nLWFzc2lzdGFudC8uL25vZGVfbW9kdWxlcy9sYW5nc21pdGgvbm9kZV9tb2R1bGVzL3V1aWQvZGlzdC9lc20tbm9kZS9yZWdleC5qcz9iNGUwIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IC9eKD86WzAtOWEtZl17OH0tWzAtOWEtZl17NH0tWzEtOF1bMC05YS1mXXszfS1bODlhYl1bMC05YS1mXXszfS1bMC05YS1mXXsxMn18MDAwMDAwMDAtMDAwMC0wMDAwLTAwMDAtMDAwMDAwMDAwMDAwfGZmZmZmZmZmLWZmZmYtZmZmZi1mZmZmLWZmZmZmZmZmZmZmZikkL2k7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/langsmith/node_modules/uuid/dist/esm-node/regex.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/langsmith/node_modules/uuid/dist/esm-node/rng.js":
/*!***********************************************************************!*\
  !*** ./node_modules/langsmith/node_modules/uuid/dist/esm-node/rng.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ rng)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(node_crypto__WEBPACK_IMPORTED_MODULE_0__);\n\nconst rnds8Pool = new Uint8Array(256); // # of random values to pre-allocate\nlet poolPtr = rnds8Pool.length;\nfunction rng() {\n  if (poolPtr > rnds8Pool.length - 16) {\n    node_crypto__WEBPACK_IMPORTED_MODULE_0___default().randomFillSync(rnds8Pool);\n    poolPtr = 0;\n  }\n  return rnds8Pool.slice(poolPtr, poolPtr += 16);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbGFuZ3NtaXRoL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLW5vZGUvcm5nLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFpQztBQUNqQyx1Q0FBdUM7QUFDdkM7QUFDZTtBQUNmO0FBQ0EsSUFBSSxpRUFBcUI7QUFDekI7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1wYXBlci1yZWFkaW5nLWFzc2lzdGFudC8uL25vZGVfbW9kdWxlcy9sYW5nc21pdGgvbm9kZV9tb2R1bGVzL3V1aWQvZGlzdC9lc20tbm9kZS9ybmcuanM/OTgyMyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3J5cHRvIGZyb20gJ25vZGU6Y3J5cHRvJztcbmNvbnN0IHJuZHM4UG9vbCA9IG5ldyBVaW50OEFycmF5KDI1Nik7IC8vICMgb2YgcmFuZG9tIHZhbHVlcyB0byBwcmUtYWxsb2NhdGVcbmxldCBwb29sUHRyID0gcm5kczhQb29sLmxlbmd0aDtcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHJuZygpIHtcbiAgaWYgKHBvb2xQdHIgPiBybmRzOFBvb2wubGVuZ3RoIC0gMTYpIHtcbiAgICBjcnlwdG8ucmFuZG9tRmlsbFN5bmMocm5kczhQb29sKTtcbiAgICBwb29sUHRyID0gMDtcbiAgfVxuICByZXR1cm4gcm5kczhQb29sLnNsaWNlKHBvb2xQdHIsIHBvb2xQdHIgKz0gMTYpO1xufSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/langsmith/node_modules/uuid/dist/esm-node/rng.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/langsmith/node_modules/uuid/dist/esm-node/stringify.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/langsmith/node_modules/uuid/dist/esm-node/stringify.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   unsafeStringify: () => (/* binding */ unsafeStringify)\n/* harmony export */ });\n/* harmony import */ var _validate_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./validate.js */ \"(rsc)/./node_modules/langsmith/node_modules/uuid/dist/esm-node/validate.js\");\n\n\n/**\n * Convert array of 16 byte values to UUID string format of the form:\n * XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX\n */\nconst byteToHex = [];\nfor (let i = 0; i < 256; ++i) {\n  byteToHex.push((i + 0x100).toString(16).slice(1));\n}\nfunction unsafeStringify(arr, offset = 0) {\n  // Note: Be careful editing this code!  It's been tuned for performance\n  // and works in ways you may not expect. See https://github.com/uuidjs/uuid/pull/434\n  //\n  // Note to future-self: No, you can't remove the `toLowerCase()` call.\n  // REF: https://github.com/uuidjs/uuid/pull/677#issuecomment-1757351351\n  return (byteToHex[arr[offset + 0]] + byteToHex[arr[offset + 1]] + byteToHex[arr[offset + 2]] + byteToHex[arr[offset + 3]] + '-' + byteToHex[arr[offset + 4]] + byteToHex[arr[offset + 5]] + '-' + byteToHex[arr[offset + 6]] + byteToHex[arr[offset + 7]] + '-' + byteToHex[arr[offset + 8]] + byteToHex[arr[offset + 9]] + '-' + byteToHex[arr[offset + 10]] + byteToHex[arr[offset + 11]] + byteToHex[arr[offset + 12]] + byteToHex[arr[offset + 13]] + byteToHex[arr[offset + 14]] + byteToHex[arr[offset + 15]]).toLowerCase();\n}\nfunction stringify(arr, offset = 0) {\n  const uuid = unsafeStringify(arr, offset);\n  // Consistency check for valid UUID.  If this throws, it's likely due to one\n  // of the following:\n  // - One or more input array values don't map to a hex octet (leading to\n  // \"undefined\" in the uuid)\n  // - Invalid input values for the RFC `version` or `variant` fields\n  if (!(0,_validate_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(uuid)) {\n    throw TypeError('Stringified UUID is invalid');\n  }\n  return uuid;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (stringify);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/langsmith/node_modules/uuid/dist/esm-node/stringify.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/langsmith/node_modules/uuid/dist/esm-node/v4.js":
/*!**********************************************************************!*\
  !*** ./node_modules/langsmith/node_modules/uuid/dist/esm-node/v4.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _native_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./native.js */ \"(rsc)/./node_modules/langsmith/node_modules/uuid/dist/esm-node/native.js\");\n/* harmony import */ var _rng_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./rng.js */ \"(rsc)/./node_modules/langsmith/node_modules/uuid/dist/esm-node/rng.js\");\n/* harmony import */ var _stringify_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./stringify.js */ \"(rsc)/./node_modules/langsmith/node_modules/uuid/dist/esm-node/stringify.js\");\n\n\n\nfunction v4(options, buf, offset) {\n  if (_native_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].randomUUID && !buf && !options) {\n    return _native_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].randomUUID();\n  }\n  options = options || {};\n  const rnds = options.random || (options.rng || _rng_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n\n  // Per 4.4, set bits for version and `clock_seq_hi_and_reserved`\n  rnds[6] = rnds[6] & 0x0f | 0x40;\n  rnds[8] = rnds[8] & 0x3f | 0x80;\n\n  // Copy bytes to buffer, if provided\n  if (buf) {\n    offset = offset || 0;\n    for (let i = 0; i < 16; ++i) {\n      buf[offset + i] = rnds[i];\n    }\n    return buf;\n  }\n  return (0,_stringify_js__WEBPACK_IMPORTED_MODULE_2__.unsafeStringify)(rnds);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (v4);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbGFuZ3NtaXRoL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLW5vZGUvdjQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFpQztBQUNOO0FBQ3NCO0FBQ2pEO0FBQ0EsTUFBTSxrREFBTTtBQUNaLFdBQVcsa0RBQU07QUFDakI7QUFDQTtBQUNBLGlEQUFpRCwrQ0FBRzs7QUFFcEQ7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixRQUFRO0FBQzVCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUyw4REFBZTtBQUN4QjtBQUNBLGlFQUFlLEVBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1wYXBlci1yZWFkaW5nLWFzc2lzdGFudC8uL25vZGVfbW9kdWxlcy9sYW5nc21pdGgvbm9kZV9tb2R1bGVzL3V1aWQvZGlzdC9lc20tbm9kZS92NC5qcz82OTA3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBuYXRpdmUgZnJvbSAnLi9uYXRpdmUuanMnO1xuaW1wb3J0IHJuZyBmcm9tICcuL3JuZy5qcyc7XG5pbXBvcnQgeyB1bnNhZmVTdHJpbmdpZnkgfSBmcm9tICcuL3N0cmluZ2lmeS5qcyc7XG5mdW5jdGlvbiB2NChvcHRpb25zLCBidWYsIG9mZnNldCkge1xuICBpZiAobmF0aXZlLnJhbmRvbVVVSUQgJiYgIWJ1ZiAmJiAhb3B0aW9ucykge1xuICAgIHJldHVybiBuYXRpdmUucmFuZG9tVVVJRCgpO1xuICB9XG4gIG9wdGlvbnMgPSBvcHRpb25zIHx8IHt9O1xuICBjb25zdCBybmRzID0gb3B0aW9ucy5yYW5kb20gfHwgKG9wdGlvbnMucm5nIHx8IHJuZykoKTtcblxuICAvLyBQZXIgNC40LCBzZXQgYml0cyBmb3IgdmVyc2lvbiBhbmQgYGNsb2NrX3NlcV9oaV9hbmRfcmVzZXJ2ZWRgXG4gIHJuZHNbNl0gPSBybmRzWzZdICYgMHgwZiB8IDB4NDA7XG4gIHJuZHNbOF0gPSBybmRzWzhdICYgMHgzZiB8IDB4ODA7XG5cbiAgLy8gQ29weSBieXRlcyB0byBidWZmZXIsIGlmIHByb3ZpZGVkXG4gIGlmIChidWYpIHtcbiAgICBvZmZzZXQgPSBvZmZzZXQgfHwgMDtcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IDE2OyArK2kpIHtcbiAgICAgIGJ1ZltvZmZzZXQgKyBpXSA9IHJuZHNbaV07XG4gICAgfVxuICAgIHJldHVybiBidWY7XG4gIH1cbiAgcmV0dXJuIHVuc2FmZVN0cmluZ2lmeShybmRzKTtcbn1cbmV4cG9ydCBkZWZhdWx0IHY0OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/langsmith/node_modules/uuid/dist/esm-node/v4.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/langsmith/node_modules/uuid/dist/esm-node/validate.js":
/*!****************************************************************************!*\
  !*** ./node_modules/langsmith/node_modules/uuid/dist/esm-node/validate.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _regex_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./regex.js */ \"(rsc)/./node_modules/langsmith/node_modules/uuid/dist/esm-node/regex.js\");\n\nfunction validate(uuid) {\n  return typeof uuid === 'string' && _regex_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].test(uuid);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (validate);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbGFuZ3NtaXRoL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLW5vZGUvdmFsaWRhdGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBK0I7QUFDL0I7QUFDQSxxQ0FBcUMsaURBQUs7QUFDMUM7QUFDQSxpRUFBZSxRQUFRIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktcGFwZXItcmVhZGluZy1hc3Npc3RhbnQvLi9ub2RlX21vZHVsZXMvbGFuZ3NtaXRoL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLW5vZGUvdmFsaWRhdGUuanM/Yjg0OSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUkVHRVggZnJvbSAnLi9yZWdleC5qcyc7XG5mdW5jdGlvbiB2YWxpZGF0ZSh1dWlkKSB7XG4gIHJldHVybiB0eXBlb2YgdXVpZCA9PT0gJ3N0cmluZycgJiYgUkVHRVgudGVzdCh1dWlkKTtcbn1cbmV4cG9ydCBkZWZhdWx0IHZhbGlkYXRlOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/langsmith/node_modules/uuid/dist/esm-node/validate.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/langsmith/dist/client.js":
/*!***********************************************!*\
  !*** ./node_modules/langsmith/dist/client.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Client: () => (/* binding */ Client),\n/* harmony export */   DEFAULT_BATCH_SIZE_LIMIT_BYTES: () => (/* binding */ DEFAULT_BATCH_SIZE_LIMIT_BYTES),\n/* harmony export */   Queue: () => (/* binding */ Queue),\n/* harmony export */   mergeRuntimeEnvIntoRunCreate: () => (/* binding */ mergeRuntimeEnvIntoRunCreate)\n/* harmony export */ });\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/langsmith/node_modules/uuid/dist/esm-node/v4.js\");\n/* harmony import */ var _utils_async_caller_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/async_caller.js */ \"(rsc)/./node_modules/langsmith/dist/utils/async_caller.js\");\n/* harmony import */ var _utils_messages_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/messages.js */ \"(rsc)/./node_modules/langsmith/dist/utils/messages.js\");\n/* harmony import */ var _utils_env_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/env.js */ \"(rsc)/./node_modules/langsmith/dist/utils/env.js\");\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./index.js */ \"(rsc)/./node_modules/langsmith/dist/index.js\");\n/* harmony import */ var _utils_uuid_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils/_uuid.js */ \"(rsc)/./node_modules/langsmith/dist/utils/_uuid.js\");\n/* harmony import */ var _utils_warn_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils/warn.js */ \"(rsc)/./node_modules/langsmith/dist/utils/warn.js\");\n/* harmony import */ var _utils_prompts_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./utils/prompts.js */ \"(rsc)/./node_modules/langsmith/dist/utils/prompts.js\");\n/* harmony import */ var _utils_error_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./utils/error.js */ \"(rsc)/./node_modules/langsmith/dist/utils/error.js\");\n/* harmony import */ var _singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./singletons/fetch.js */ \"(rsc)/./node_modules/langsmith/dist/singletons/fetch.js\");\n/* harmony import */ var _utils_fast_safe_stringify_index_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./utils/fast-safe-stringify/index.js */ \"(rsc)/./node_modules/langsmith/dist/utils/fast-safe-stringify/index.js\");\n\n\n\n\n\n\n\n\n\n\n\nfunction mergeRuntimeEnvIntoRunCreate(run) {\n    const runtimeEnv = (0,_utils_env_js__WEBPACK_IMPORTED_MODULE_2__.getRuntimeEnvironment)();\n    const envVars = (0,_utils_env_js__WEBPACK_IMPORTED_MODULE_2__.getLangChainEnvVarsMetadata)();\n    const extra = run.extra ?? {};\n    const metadata = extra.metadata;\n    run.extra = {\n        ...extra,\n        runtime: {\n            ...runtimeEnv,\n            ...extra?.runtime,\n        },\n        metadata: {\n            ...envVars,\n            ...(envVars.revision_id || run.revision_id\n                ? { revision_id: run.revision_id ?? envVars.revision_id }\n                : {}),\n            ...metadata,\n        },\n    };\n    return run;\n}\nconst getTracingSamplingRate = () => {\n    const samplingRateStr = (0,_utils_env_js__WEBPACK_IMPORTED_MODULE_2__.getLangSmithEnvironmentVariable)(\"TRACING_SAMPLING_RATE\");\n    if (samplingRateStr === undefined) {\n        return undefined;\n    }\n    const samplingRate = parseFloat(samplingRateStr);\n    if (samplingRate < 0 || samplingRate > 1) {\n        throw new Error(`LANGSMITH_TRACING_SAMPLING_RATE must be between 0 and 1 if set. Got: ${samplingRate}`);\n    }\n    return samplingRate;\n};\n// utility functions\nconst isLocalhost = (url) => {\n    const strippedUrl = url.replace(\"http://\", \"\").replace(\"https://\", \"\");\n    const hostname = strippedUrl.split(\"/\")[0].split(\":\")[0];\n    return (hostname === \"localhost\" || hostname === \"127.0.0.1\" || hostname === \"::1\");\n};\nasync function toArray(iterable) {\n    const result = [];\n    for await (const item of iterable) {\n        result.push(item);\n    }\n    return result;\n}\nfunction trimQuotes(str) {\n    if (str === undefined) {\n        return undefined;\n    }\n    return str\n        .trim()\n        .replace(/^\"(.*)\"$/, \"$1\")\n        .replace(/^'(.*)'$/, \"$1\");\n}\nconst handle429 = async (response) => {\n    if (response?.status === 429) {\n        const retryAfter = parseInt(response.headers.get(\"retry-after\") ?? \"30\", 10) * 1000;\n        if (retryAfter > 0) {\n            await new Promise((resolve) => setTimeout(resolve, retryAfter));\n            // Return directly after calling this check\n            return true;\n        }\n    }\n    // Fall back to existing status checks\n    return false;\n};\nclass Queue {\n    constructor() {\n        Object.defineProperty(this, \"items\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: []\n        });\n        Object.defineProperty(this, \"sizeBytes\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 0\n        });\n    }\n    peek() {\n        return this.items[0];\n    }\n    push(item) {\n        let itemPromiseResolve;\n        const itemPromise = new Promise((resolve) => {\n            // Setting itemPromiseResolve is synchronous with promise creation:\n            // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/Promise\n            itemPromiseResolve = resolve;\n        });\n        const size = (0,_utils_fast_safe_stringify_index_js__WEBPACK_IMPORTED_MODULE_9__.stringify)(item.item).length;\n        this.items.push({\n            action: item.action,\n            payload: item.item,\n            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n            itemPromiseResolve: itemPromiseResolve,\n            itemPromise,\n            size,\n        });\n        this.sizeBytes += size;\n        return itemPromise;\n    }\n    pop(upToSizeBytes) {\n        if (upToSizeBytes < 1) {\n            throw new Error(\"Number of bytes to pop off may not be less than 1.\");\n        }\n        const popped = [];\n        let poppedSizeBytes = 0;\n        // Pop items until we reach or exceed the size limit\n        while (poppedSizeBytes + (this.peek()?.size ?? 0) < upToSizeBytes &&\n            this.items.length > 0) {\n            const item = this.items.shift();\n            if (item) {\n                popped.push(item);\n                poppedSizeBytes += item.size;\n                this.sizeBytes -= item.size;\n            }\n        }\n        // If there is an item on the queue we were unable to pop,\n        // just return it as a single batch.\n        if (popped.length === 0 && this.items.length > 0) {\n            const item = this.items.shift();\n            popped.push(item);\n            poppedSizeBytes += item.size;\n            this.sizeBytes -= item.size;\n        }\n        return [\n            popped.map((it) => ({ action: it.action, item: it.payload })),\n            () => popped.forEach((it) => it.itemPromiseResolve()),\n        ];\n    }\n}\n// 20 MB\nconst DEFAULT_BATCH_SIZE_LIMIT_BYTES = 20_971_520;\nconst SERVER_INFO_REQUEST_TIMEOUT = 1000;\nclass Client {\n    constructor(config = {}) {\n        Object.defineProperty(this, \"apiKey\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"apiUrl\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"webUrl\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"caller\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"batchIngestCaller\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"timeout_ms\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"_tenantId\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: null\n        });\n        Object.defineProperty(this, \"hideInputs\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"hideOutputs\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"tracingSampleRate\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"filteredPostUuids\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: new Set()\n        });\n        Object.defineProperty(this, \"autoBatchTracing\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: true\n        });\n        Object.defineProperty(this, \"autoBatchQueue\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: new Queue()\n        });\n        Object.defineProperty(this, \"autoBatchTimeout\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"autoBatchInitialDelayMs\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 250\n        });\n        Object.defineProperty(this, \"autoBatchAggregationDelayMs\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: 50\n        });\n        Object.defineProperty(this, \"batchSizeBytesLimit\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"fetchOptions\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"settings\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"blockOnRootRunFinalization\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: true\n        });\n        Object.defineProperty(this, \"_serverInfo\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        Object.defineProperty(this, \"_getServerInfoPromise\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        const defaultConfig = Client.getDefaultClientConfig();\n        this.tracingSampleRate = getTracingSamplingRate();\n        this.apiUrl = trimQuotes(config.apiUrl ?? defaultConfig.apiUrl) ?? \"\";\n        if (this.apiUrl.endsWith(\"/\")) {\n            this.apiUrl = this.apiUrl.slice(0, -1);\n        }\n        this.apiKey = trimQuotes(config.apiKey ?? defaultConfig.apiKey);\n        this.webUrl = trimQuotes(config.webUrl ?? defaultConfig.webUrl);\n        if (this.webUrl?.endsWith(\"/\")) {\n            this.webUrl = this.webUrl.slice(0, -1);\n        }\n        this.timeout_ms = config.timeout_ms ?? 12_000;\n        this.caller = new _utils_async_caller_js__WEBPACK_IMPORTED_MODULE_0__.AsyncCaller(config.callerOptions ?? {});\n        this.batchIngestCaller = new _utils_async_caller_js__WEBPACK_IMPORTED_MODULE_0__.AsyncCaller({\n            ...(config.callerOptions ?? {}),\n            onFailedResponseHook: handle429,\n        });\n        this.hideInputs =\n            config.hideInputs ?? config.anonymizer ?? defaultConfig.hideInputs;\n        this.hideOutputs =\n            config.hideOutputs ?? config.anonymizer ?? defaultConfig.hideOutputs;\n        this.autoBatchTracing = config.autoBatchTracing ?? this.autoBatchTracing;\n        this.blockOnRootRunFinalization =\n            config.blockOnRootRunFinalization ?? this.blockOnRootRunFinalization;\n        this.batchSizeBytesLimit = config.batchSizeBytesLimit;\n        this.fetchOptions = config.fetchOptions || {};\n    }\n    static getDefaultClientConfig() {\n        const apiKey = (0,_utils_env_js__WEBPACK_IMPORTED_MODULE_2__.getLangSmithEnvironmentVariable)(\"API_KEY\");\n        const apiUrl = (0,_utils_env_js__WEBPACK_IMPORTED_MODULE_2__.getLangSmithEnvironmentVariable)(\"ENDPOINT\") ??\n            \"https://api.smith.langchain.com\";\n        const hideInputs = (0,_utils_env_js__WEBPACK_IMPORTED_MODULE_2__.getLangSmithEnvironmentVariable)(\"HIDE_INPUTS\") === \"true\";\n        const hideOutputs = (0,_utils_env_js__WEBPACK_IMPORTED_MODULE_2__.getLangSmithEnvironmentVariable)(\"HIDE_OUTPUTS\") === \"true\";\n        return {\n            apiUrl: apiUrl,\n            apiKey: apiKey,\n            webUrl: undefined,\n            hideInputs: hideInputs,\n            hideOutputs: hideOutputs,\n        };\n    }\n    getHostUrl() {\n        if (this.webUrl) {\n            return this.webUrl;\n        }\n        else if (isLocalhost(this.apiUrl)) {\n            this.webUrl = \"http://localhost:3000\";\n            return this.webUrl;\n        }\n        else if (this.apiUrl.includes(\"/api\") &&\n            !this.apiUrl.split(\".\", 1)[0].endsWith(\"api\")) {\n            this.webUrl = this.apiUrl.replace(\"/api\", \"\");\n            return this.webUrl;\n        }\n        else if (this.apiUrl.split(\".\", 1)[0].includes(\"dev\")) {\n            this.webUrl = \"https://dev.smith.langchain.com\";\n            return this.webUrl;\n        }\n        else if (this.apiUrl.split(\".\", 1)[0].includes(\"eu\")) {\n            this.webUrl = \"https://eu.smith.langchain.com\";\n            return this.webUrl;\n        }\n        else {\n            this.webUrl = \"https://smith.langchain.com\";\n            return this.webUrl;\n        }\n    }\n    get headers() {\n        const headers = {\n            \"User-Agent\": `langsmith-js/${_index_js__WEBPACK_IMPORTED_MODULE_3__.__version__}`,\n        };\n        if (this.apiKey) {\n            headers[\"x-api-key\"] = `${this.apiKey}`;\n        }\n        return headers;\n    }\n    processInputs(inputs) {\n        if (this.hideInputs === false) {\n            return inputs;\n        }\n        if (this.hideInputs === true) {\n            return {};\n        }\n        if (typeof this.hideInputs === \"function\") {\n            return this.hideInputs(inputs);\n        }\n        return inputs;\n    }\n    processOutputs(outputs) {\n        if (this.hideOutputs === false) {\n            return outputs;\n        }\n        if (this.hideOutputs === true) {\n            return {};\n        }\n        if (typeof this.hideOutputs === \"function\") {\n            return this.hideOutputs(outputs);\n        }\n        return outputs;\n    }\n    prepareRunCreateOrUpdateInputs(run) {\n        const runParams = { ...run };\n        if (runParams.inputs !== undefined) {\n            runParams.inputs = this.processInputs(runParams.inputs);\n        }\n        if (runParams.outputs !== undefined) {\n            runParams.outputs = this.processOutputs(runParams.outputs);\n        }\n        return runParams;\n    }\n    async _getResponse(path, queryParams) {\n        const paramsString = queryParams?.toString() ?? \"\";\n        const url = `${this.apiUrl}${path}?${paramsString}`;\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), url, {\n            method: \"GET\",\n            headers: this.headers,\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await (0,_utils_error_js__WEBPACK_IMPORTED_MODULE_7__.raiseForStatus)(response, `Failed to fetch ${path}`);\n        return response;\n    }\n    async _get(path, queryParams) {\n        const response = await this._getResponse(path, queryParams);\n        return response.json();\n    }\n    async *_getPaginated(path, queryParams = new URLSearchParams(), transform) {\n        let offset = Number(queryParams.get(\"offset\")) || 0;\n        const limit = Number(queryParams.get(\"limit\")) || 100;\n        while (true) {\n            queryParams.set(\"offset\", String(offset));\n            queryParams.set(\"limit\", String(limit));\n            const url = `${this.apiUrl}${path}?${queryParams}`;\n            const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), url, {\n                method: \"GET\",\n                headers: this.headers,\n                signal: AbortSignal.timeout(this.timeout_ms),\n                ...this.fetchOptions,\n            });\n            await (0,_utils_error_js__WEBPACK_IMPORTED_MODULE_7__.raiseForStatus)(response, `Failed to fetch ${path}`);\n            const items = transform\n                ? transform(await response.json())\n                : await response.json();\n            if (items.length === 0) {\n                break;\n            }\n            yield items;\n            if (items.length < limit) {\n                break;\n            }\n            offset += items.length;\n        }\n    }\n    async *_getCursorPaginatedList(path, body = null, requestMethod = \"POST\", dataKey = \"runs\") {\n        const bodyParams = body ? { ...body } : {};\n        while (true) {\n            const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), `${this.apiUrl}${path}`, {\n                method: requestMethod,\n                headers: { ...this.headers, \"Content-Type\": \"application/json\" },\n                signal: AbortSignal.timeout(this.timeout_ms),\n                ...this.fetchOptions,\n                body: JSON.stringify(bodyParams),\n            });\n            const responseBody = await response.json();\n            if (!responseBody) {\n                break;\n            }\n            if (!responseBody[dataKey]) {\n                break;\n            }\n            yield responseBody[dataKey];\n            const cursors = responseBody.cursors;\n            if (!cursors) {\n                break;\n            }\n            if (!cursors.next) {\n                break;\n            }\n            bodyParams.cursor = cursors.next;\n        }\n    }\n    _filterForSampling(runs, patch = false) {\n        if (this.tracingSampleRate === undefined) {\n            return runs;\n        }\n        if (patch) {\n            const sampled = [];\n            for (const run of runs) {\n                if (!this.filteredPostUuids.has(run.id)) {\n                    sampled.push(run);\n                }\n                else {\n                    this.filteredPostUuids.delete(run.id);\n                }\n            }\n            return sampled;\n        }\n        else {\n            const sampled = [];\n            for (const run of runs) {\n                if ((run.id !== run.trace_id &&\n                    !this.filteredPostUuids.has(run.trace_id)) ||\n                    Math.random() < this.tracingSampleRate) {\n                    sampled.push(run);\n                }\n                else {\n                    this.filteredPostUuids.add(run.id);\n                }\n            }\n            return sampled;\n        }\n    }\n    async _getBatchSizeLimitBytes() {\n        const serverInfo = await this._ensureServerInfo();\n        return (this.batchSizeBytesLimit ??\n            serverInfo.batch_ingest_config?.size_limit_bytes ??\n            DEFAULT_BATCH_SIZE_LIMIT_BYTES);\n    }\n    async drainAutoBatchQueue() {\n        while (this.autoBatchQueue.items.length >= 0) {\n            const [batch, done] = this.autoBatchQueue.pop(await this._getBatchSizeLimitBytes());\n            if (!batch.length) {\n                done();\n                return;\n            }\n            try {\n                const ingestParams = {\n                    runCreates: batch\n                        .filter((item) => item.action === \"create\")\n                        .map((item) => item.item),\n                    runUpdates: batch\n                        .filter((item) => item.action === \"update\")\n                        .map((item) => item.item),\n                };\n                const serverInfo = await this._ensureServerInfo();\n                if (serverInfo?.batch_ingest_config?.use_multipart_endpoint) {\n                    await this.multipartIngestRuns(ingestParams);\n                }\n                else {\n                    await this.batchIngestRuns(ingestParams);\n                }\n            }\n            finally {\n                done();\n            }\n        }\n    }\n    async processRunOperation(item, immediatelyTriggerBatch) {\n        const oldTimeout = this.autoBatchTimeout;\n        clearTimeout(this.autoBatchTimeout);\n        this.autoBatchTimeout = undefined;\n        if (item.action === \"create\") {\n            item.item = mergeRuntimeEnvIntoRunCreate(item.item);\n        }\n        const itemPromise = this.autoBatchQueue.push(item);\n        const sizeLimitBytes = await this._getBatchSizeLimitBytes();\n        if (immediatelyTriggerBatch ||\n            this.autoBatchQueue.sizeBytes > sizeLimitBytes) {\n            await this.drainAutoBatchQueue().catch(console.error);\n        }\n        if (this.autoBatchQueue.items.length > 0) {\n            this.autoBatchTimeout = setTimeout(() => {\n                this.autoBatchTimeout = undefined;\n                // This error would happen in the background and is uncatchable\n                // from the outside. So just log instead.\n                void this.drainAutoBatchQueue().catch(console.error);\n            }, oldTimeout\n                ? this.autoBatchAggregationDelayMs\n                : this.autoBatchInitialDelayMs);\n        }\n        return itemPromise;\n    }\n    async _getServerInfo() {\n        const response = await (0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)()(`${this.apiUrl}/info`, {\n            method: \"GET\",\n            headers: { Accept: \"application/json\" },\n            signal: AbortSignal.timeout(SERVER_INFO_REQUEST_TIMEOUT),\n            ...this.fetchOptions,\n        });\n        await (0,_utils_error_js__WEBPACK_IMPORTED_MODULE_7__.raiseForStatus)(response, \"get server info\");\n        return response.json();\n    }\n    async _ensureServerInfo() {\n        if (this._getServerInfoPromise === undefined) {\n            this._getServerInfoPromise = (async () => {\n                if (this._serverInfo === undefined) {\n                    try {\n                        this._serverInfo = await this._getServerInfo();\n                    }\n                    catch (e) {\n                        console.warn(`[WARNING]: LangSmith failed to fetch info on supported operations. Falling back to single calls and default limits.`);\n                    }\n                }\n                return this._serverInfo ?? {};\n            })();\n        }\n        return this._getServerInfoPromise.then((serverInfo) => {\n            if (this._serverInfo === undefined) {\n                this._getServerInfoPromise = undefined;\n            }\n            return serverInfo;\n        });\n    }\n    async _getSettings() {\n        if (!this.settings) {\n            this.settings = this._get(\"/settings\");\n        }\n        return await this.settings;\n    }\n    async createRun(run) {\n        if (!this._filterForSampling([run]).length) {\n            return;\n        }\n        const headers = { ...this.headers, \"Content-Type\": \"application/json\" };\n        const session_name = run.project_name;\n        delete run.project_name;\n        const runCreate = this.prepareRunCreateOrUpdateInputs({\n            session_name,\n            ...run,\n            start_time: run.start_time ?? Date.now(),\n        });\n        if (this.autoBatchTracing &&\n            runCreate.trace_id !== undefined &&\n            runCreate.dotted_order !== undefined) {\n            void this.processRunOperation({\n                action: \"create\",\n                item: runCreate,\n            }).catch(console.error);\n            return;\n        }\n        const mergedRunCreateParam = mergeRuntimeEnvIntoRunCreate(runCreate);\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), `${this.apiUrl}/runs`, {\n            method: \"POST\",\n            headers,\n            body: (0,_utils_fast_safe_stringify_index_js__WEBPACK_IMPORTED_MODULE_9__.stringify)(mergedRunCreateParam),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await (0,_utils_error_js__WEBPACK_IMPORTED_MODULE_7__.raiseForStatus)(response, \"create run\", true);\n    }\n    /**\n     * Batch ingest/upsert multiple runs in the Langsmith system.\n     * @param runs\n     */\n    async batchIngestRuns({ runCreates, runUpdates, }) {\n        if (runCreates === undefined && runUpdates === undefined) {\n            return;\n        }\n        let preparedCreateParams = runCreates?.map((create) => this.prepareRunCreateOrUpdateInputs(create)) ?? [];\n        let preparedUpdateParams = runUpdates?.map((update) => this.prepareRunCreateOrUpdateInputs(update)) ?? [];\n        if (preparedCreateParams.length > 0 && preparedUpdateParams.length > 0) {\n            const createById = preparedCreateParams.reduce((params, run) => {\n                if (!run.id) {\n                    return params;\n                }\n                params[run.id] = run;\n                return params;\n            }, {});\n            const standaloneUpdates = [];\n            for (const updateParam of preparedUpdateParams) {\n                if (updateParam.id !== undefined && createById[updateParam.id]) {\n                    createById[updateParam.id] = {\n                        ...createById[updateParam.id],\n                        ...updateParam,\n                    };\n                }\n                else {\n                    standaloneUpdates.push(updateParam);\n                }\n            }\n            preparedCreateParams = Object.values(createById);\n            preparedUpdateParams = standaloneUpdates;\n        }\n        const rawBatch = {\n            post: this._filterForSampling(preparedCreateParams),\n            patch: this._filterForSampling(preparedUpdateParams, true),\n        };\n        if (!rawBatch.post.length && !rawBatch.patch.length) {\n            return;\n        }\n        const serverInfo = await this._ensureServerInfo();\n        if (serverInfo.version === undefined) {\n            this.autoBatchTracing = false;\n            for (const preparedCreateParam of rawBatch.post) {\n                await this.createRun(preparedCreateParam);\n            }\n            for (const preparedUpdateParam of rawBatch.patch) {\n                if (preparedUpdateParam.id !== undefined) {\n                    await this.updateRun(preparedUpdateParam.id, preparedUpdateParam);\n                }\n            }\n            return;\n        }\n        const batchChunks = {\n            post: [],\n            patch: [],\n        };\n        for (const k of [\"post\", \"patch\"]) {\n            const key = k;\n            const batchItems = rawBatch[key].reverse();\n            let batchItem = batchItems.pop();\n            while (batchItem !== undefined) {\n                batchChunks[key].push(batchItem);\n                batchItem = batchItems.pop();\n            }\n        }\n        if (batchChunks.post.length > 0 || batchChunks.patch.length > 0) {\n            await this._postBatchIngestRuns((0,_utils_fast_safe_stringify_index_js__WEBPACK_IMPORTED_MODULE_9__.stringify)(batchChunks));\n        }\n    }\n    async _postBatchIngestRuns(body) {\n        const headers = {\n            ...this.headers,\n            \"Content-Type\": \"application/json\",\n            Accept: \"application/json\",\n        };\n        const response = await this.batchIngestCaller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), `${this.apiUrl}/runs/batch`, {\n            method: \"POST\",\n            headers,\n            body: body,\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await (0,_utils_error_js__WEBPACK_IMPORTED_MODULE_7__.raiseForStatus)(response, \"batch create run\", true);\n    }\n    /**\n     * Batch ingest/upsert multiple runs in the Langsmith system.\n     * @param runs\n     */\n    async multipartIngestRuns({ runCreates, runUpdates, }) {\n        if (runCreates === undefined && runUpdates === undefined) {\n            return;\n        }\n        // transform and convert to dicts\n        const allAttachments = {};\n        let preparedCreateParams = [];\n        for (const create of runCreates ?? []) {\n            const preparedCreate = this.prepareRunCreateOrUpdateInputs(create);\n            if (preparedCreate.id !== undefined &&\n                preparedCreate.attachments !== undefined) {\n                allAttachments[preparedCreate.id] = preparedCreate.attachments;\n            }\n            delete preparedCreate.attachments;\n            preparedCreateParams.push(preparedCreate);\n        }\n        let preparedUpdateParams = [];\n        for (const update of runUpdates ?? []) {\n            preparedUpdateParams.push(this.prepareRunCreateOrUpdateInputs(update));\n        }\n        // require trace_id and dotted_order\n        const invalidRunCreate = preparedCreateParams.find((runCreate) => {\n            return (runCreate.trace_id === undefined || runCreate.dotted_order === undefined);\n        });\n        if (invalidRunCreate !== undefined) {\n            throw new Error(`Multipart ingest requires \"trace_id\" and \"dotted_order\" to be set when creating a run`);\n        }\n        const invalidRunUpdate = preparedUpdateParams.find((runUpdate) => {\n            return (runUpdate.trace_id === undefined || runUpdate.dotted_order === undefined);\n        });\n        if (invalidRunUpdate !== undefined) {\n            throw new Error(`Multipart ingest requires \"trace_id\" and \"dotted_order\" to be set when updating a run`);\n        }\n        // combine post and patch dicts where possible\n        if (preparedCreateParams.length > 0 && preparedUpdateParams.length > 0) {\n            const createById = preparedCreateParams.reduce((params, run) => {\n                if (!run.id) {\n                    return params;\n                }\n                params[run.id] = run;\n                return params;\n            }, {});\n            const standaloneUpdates = [];\n            for (const updateParam of preparedUpdateParams) {\n                if (updateParam.id !== undefined && createById[updateParam.id]) {\n                    createById[updateParam.id] = {\n                        ...createById[updateParam.id],\n                        ...updateParam,\n                    };\n                }\n                else {\n                    standaloneUpdates.push(updateParam);\n                }\n            }\n            preparedCreateParams = Object.values(createById);\n            preparedUpdateParams = standaloneUpdates;\n        }\n        if (preparedCreateParams.length === 0 &&\n            preparedUpdateParams.length === 0) {\n            return;\n        }\n        // send the runs in multipart requests\n        const accumulatedContext = [];\n        const accumulatedParts = [];\n        for (const [method, payloads] of [\n            [\"post\", preparedCreateParams],\n            [\"patch\", preparedUpdateParams],\n        ]) {\n            for (const originalPayload of payloads) {\n                // collect fields to be sent as separate parts\n                const { inputs, outputs, events, ...payload } = originalPayload;\n                const fields = { inputs, outputs, events };\n                // encode the main run payload\n                const stringifiedPayload = (0,_utils_fast_safe_stringify_index_js__WEBPACK_IMPORTED_MODULE_9__.stringify)(payload);\n                accumulatedParts.push({\n                    name: `${method}.${payload.id}`,\n                    payload: new Blob([stringifiedPayload], {\n                        type: `application/json; length=${stringifiedPayload.length}`, // encoding=gzip\n                    }),\n                });\n                // encode the fields we collected\n                for (const [key, value] of Object.entries(fields)) {\n                    if (value === undefined) {\n                        continue;\n                    }\n                    const stringifiedValue = (0,_utils_fast_safe_stringify_index_js__WEBPACK_IMPORTED_MODULE_9__.stringify)(value);\n                    accumulatedParts.push({\n                        name: `${method}.${payload.id}.${key}`,\n                        payload: new Blob([stringifiedValue], {\n                            type: `application/json; length=${stringifiedValue.length}`,\n                        }),\n                    });\n                }\n                // encode the attachments\n                if (payload.id !== undefined) {\n                    const attachments = allAttachments[payload.id];\n                    if (attachments) {\n                        delete allAttachments[payload.id];\n                        for (const [name, [contentType, content]] of Object.entries(attachments)) {\n                            accumulatedParts.push({\n                                name: `attachment.${payload.id}.${name}`,\n                                payload: new Blob([content], {\n                                    type: `${contentType}; length=${content.length}`,\n                                }),\n                            });\n                        }\n                    }\n                }\n                // compute context\n                accumulatedContext.push(`trace=${payload.trace_id},id=${payload.id}`);\n            }\n        }\n        await this._sendMultipartRequest(accumulatedParts, accumulatedContext.join(\"; \"));\n    }\n    async _sendMultipartRequest(parts, context) {\n        try {\n            const formData = new FormData();\n            for (const part of parts) {\n                formData.append(part.name, part.payload);\n            }\n            await this.batchIngestCaller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), `${this.apiUrl}/runs/multipart`, {\n                method: \"POST\",\n                headers: {\n                    ...this.headers,\n                },\n                body: formData,\n                signal: AbortSignal.timeout(this.timeout_ms),\n                ...this.fetchOptions,\n            });\n        }\n        catch (e) {\n            let errorMessage = \"Failed to multipart ingest runs\";\n            // eslint-disable-next-line no-instanceof/no-instanceof\n            if (e instanceof Error) {\n                errorMessage += `: ${e.stack || e.message}`;\n            }\n            else {\n                errorMessage += `: ${String(e)}`;\n            }\n            console.warn(`${errorMessage.trim()}\\n\\nContext: ${context}`);\n        }\n    }\n    async updateRun(runId, run) {\n        (0,_utils_uuid_js__WEBPACK_IMPORTED_MODULE_4__.assertUuid)(runId);\n        if (run.inputs) {\n            run.inputs = this.processInputs(run.inputs);\n        }\n        if (run.outputs) {\n            run.outputs = this.processOutputs(run.outputs);\n        }\n        // TODO: Untangle types\n        const data = { ...run, id: runId };\n        if (!this._filterForSampling([data], true).length) {\n            return;\n        }\n        if (this.autoBatchTracing &&\n            data.trace_id !== undefined &&\n            data.dotted_order !== undefined) {\n            if (run.end_time !== undefined &&\n                data.parent_run_id === undefined &&\n                this.blockOnRootRunFinalization) {\n                // Trigger a batch as soon as a root trace ends and block to ensure trace finishes\n                // in serverless environments.\n                await this.processRunOperation({ action: \"update\", item: data }, true);\n                return;\n            }\n            else {\n                void this.processRunOperation({ action: \"update\", item: data }).catch(console.error);\n            }\n            return;\n        }\n        const headers = { ...this.headers, \"Content-Type\": \"application/json\" };\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), `${this.apiUrl}/runs/${runId}`, {\n            method: \"PATCH\",\n            headers,\n            body: (0,_utils_fast_safe_stringify_index_js__WEBPACK_IMPORTED_MODULE_9__.stringify)(run),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await (0,_utils_error_js__WEBPACK_IMPORTED_MODULE_7__.raiseForStatus)(response, \"update run\", true);\n    }\n    async readRun(runId, { loadChildRuns } = { loadChildRuns: false }) {\n        (0,_utils_uuid_js__WEBPACK_IMPORTED_MODULE_4__.assertUuid)(runId);\n        let run = await this._get(`/runs/${runId}`);\n        if (loadChildRuns && run.child_run_ids) {\n            run = await this._loadChildRuns(run);\n        }\n        return run;\n    }\n    async getRunUrl({ runId, run, projectOpts, }) {\n        if (run !== undefined) {\n            let sessionId;\n            if (run.session_id) {\n                sessionId = run.session_id;\n            }\n            else if (projectOpts?.projectName) {\n                sessionId = (await this.readProject({ projectName: projectOpts?.projectName })).id;\n            }\n            else if (projectOpts?.projectId) {\n                sessionId = projectOpts?.projectId;\n            }\n            else {\n                const project = await this.readProject({\n                    projectName: (0,_utils_env_js__WEBPACK_IMPORTED_MODULE_2__.getLangSmithEnvironmentVariable)(\"PROJECT\") || \"default\",\n                });\n                sessionId = project.id;\n            }\n            const tenantId = await this._getTenantId();\n            return `${this.getHostUrl()}/o/${tenantId}/projects/p/${sessionId}/r/${run.id}?poll=true`;\n        }\n        else if (runId !== undefined) {\n            const run_ = await this.readRun(runId);\n            if (!run_.app_path) {\n                throw new Error(`Run ${runId} has no app_path`);\n            }\n            const baseUrl = this.getHostUrl();\n            return `${baseUrl}${run_.app_path}`;\n        }\n        else {\n            throw new Error(\"Must provide either runId or run\");\n        }\n    }\n    async _loadChildRuns(run) {\n        const childRuns = await toArray(this.listRuns({ id: run.child_run_ids }));\n        const treemap = {};\n        const runs = {};\n        // TODO: make dotted order required when the migration finishes\n        childRuns.sort((a, b) => (a?.dotted_order ?? \"\").localeCompare(b?.dotted_order ?? \"\"));\n        for (const childRun of childRuns) {\n            if (childRun.parent_run_id === null ||\n                childRun.parent_run_id === undefined) {\n                throw new Error(`Child run ${childRun.id} has no parent`);\n            }\n            if (!(childRun.parent_run_id in treemap)) {\n                treemap[childRun.parent_run_id] = [];\n            }\n            treemap[childRun.parent_run_id].push(childRun);\n            runs[childRun.id] = childRun;\n        }\n        run.child_runs = treemap[run.id] || [];\n        for (const runId in treemap) {\n            if (runId !== run.id) {\n                runs[runId].child_runs = treemap[runId];\n            }\n        }\n        return run;\n    }\n    /**\n     * List runs from the LangSmith server.\n     * @param projectId - The ID of the project to filter by.\n     * @param projectName - The name of the project to filter by.\n     * @param parentRunId - The ID of the parent run to filter by.\n     * @param traceId - The ID of the trace to filter by.\n     * @param referenceExampleId - The ID of the reference example to filter by.\n     * @param startTime - The start time to filter by.\n     * @param isRoot - Indicates whether to only return root runs.\n     * @param runType - The run type to filter by.\n     * @param error - Indicates whether to filter by error runs.\n     * @param id - The ID of the run to filter by.\n     * @param query - The query string to filter by.\n     * @param filter - The filter string to apply to the run spans.\n     * @param traceFilter - The filter string to apply on the root run of the trace.\n     * @param limit - The maximum number of runs to retrieve.\n     * @returns {AsyncIterable<Run>} - The runs.\n     *\n     * @example\n     * // List all runs in a project\n     * const projectRuns = client.listRuns({ projectName: \"<your_project>\" });\n     *\n     * @example\n     * // List LLM and Chat runs in the last 24 hours\n     * const todaysLLMRuns = client.listRuns({\n     *   projectName: \"<your_project>\",\n     *   start_time: new Date(Date.now() - 24 * 60 * 60 * 1000),\n     *   run_type: \"llm\",\n     * });\n     *\n     * @example\n     * // List traces in a project\n     * const rootRuns = client.listRuns({\n     *   projectName: \"<your_project>\",\n     *   execution_order: 1,\n     * });\n     *\n     * @example\n     * // List runs without errors\n     * const correctRuns = client.listRuns({\n     *   projectName: \"<your_project>\",\n     *   error: false,\n     * });\n     *\n     * @example\n     * // List runs by run ID\n     * const runIds = [\n     *   \"a36092d2-4ad5-4fb4-9c0d-0dba9a2ed836\",\n     *   \"9398e6be-964f-4aa4-8ae9-ad78cd4b7074\",\n     * ];\n     * const selectedRuns = client.listRuns({ run_ids: runIds });\n     *\n     * @example\n     * // List all \"chain\" type runs that took more than 10 seconds and had `total_tokens` greater than 5000\n     * const chainRuns = client.listRuns({\n     *   projectName: \"<your_project>\",\n     *   filter: 'and(eq(run_type, \"chain\"), gt(latency, 10), gt(total_tokens, 5000))',\n     * });\n     *\n     * @example\n     * // List all runs called \"extractor\" whose root of the trace was assigned feedback \"user_score\" score of 1\n     * const goodExtractorRuns = client.listRuns({\n     *   projectName: \"<your_project>\",\n     *   filter: 'eq(name, \"extractor\")',\n     *   traceFilter: 'and(eq(feedback_key, \"user_score\"), eq(feedback_score, 1))',\n     * });\n     *\n     * @example\n     * // List all runs that started after a specific timestamp and either have \"error\" not equal to null or a \"Correctness\" feedback score equal to 0\n     * const complexRuns = client.listRuns({\n     *   projectName: \"<your_project>\",\n     *   filter: 'and(gt(start_time, \"2023-07-15T12:34:56Z\"), or(neq(error, null), and(eq(feedback_key, \"Correctness\"), eq(feedback_score, 0.0))))',\n     * });\n     *\n     * @example\n     * // List all runs where `tags` include \"experimental\" or \"beta\" and `latency` is greater than 2 seconds\n     * const taggedRuns = client.listRuns({\n     *   projectName: \"<your_project>\",\n     *   filter: 'and(or(has(tags, \"experimental\"), has(tags, \"beta\")), gt(latency, 2))',\n     * });\n     */\n    async *listRuns(props) {\n        const { projectId, projectName, parentRunId, traceId, referenceExampleId, startTime, executionOrder, isRoot, runType, error, id, query, filter, traceFilter, treeFilter, limit, select, } = props;\n        let projectIds = [];\n        if (projectId) {\n            projectIds = Array.isArray(projectId) ? projectId : [projectId];\n        }\n        if (projectName) {\n            const projectNames = Array.isArray(projectName)\n                ? projectName\n                : [projectName];\n            const projectIds_ = await Promise.all(projectNames.map((name) => this.readProject({ projectName: name }).then((project) => project.id)));\n            projectIds.push(...projectIds_);\n        }\n        const default_select = [\n            \"app_path\",\n            \"child_run_ids\",\n            \"completion_cost\",\n            \"completion_tokens\",\n            \"dotted_order\",\n            \"end_time\",\n            \"error\",\n            \"events\",\n            \"extra\",\n            \"feedback_stats\",\n            \"first_token_time\",\n            \"id\",\n            \"inputs\",\n            \"name\",\n            \"outputs\",\n            \"parent_run_id\",\n            \"parent_run_ids\",\n            \"prompt_cost\",\n            \"prompt_tokens\",\n            \"reference_example_id\",\n            \"run_type\",\n            \"session_id\",\n            \"start_time\",\n            \"status\",\n            \"tags\",\n            \"total_cost\",\n            \"total_tokens\",\n            \"trace_id\",\n        ];\n        const body = {\n            session: projectIds.length ? projectIds : null,\n            run_type: runType,\n            reference_example: referenceExampleId,\n            query,\n            filter,\n            trace_filter: traceFilter,\n            tree_filter: treeFilter,\n            execution_order: executionOrder,\n            parent_run: parentRunId,\n            start_time: startTime ? startTime.toISOString() : null,\n            error,\n            id,\n            limit,\n            trace: traceId,\n            select: select ? select : default_select,\n            is_root: isRoot,\n        };\n        let runsYielded = 0;\n        for await (const runs of this._getCursorPaginatedList(\"/runs/query\", body)) {\n            if (limit) {\n                if (runsYielded >= limit) {\n                    break;\n                }\n                if (runs.length + runsYielded > limit) {\n                    const newRuns = runs.slice(0, limit - runsYielded);\n                    yield* newRuns;\n                    break;\n                }\n                runsYielded += runs.length;\n                yield* runs;\n            }\n            else {\n                yield* runs;\n            }\n        }\n    }\n    async getRunStats({ id, trace, parentRun, runType, projectNames, projectIds, referenceExampleIds, startTime, endTime, error, query, filter, traceFilter, treeFilter, isRoot, dataSourceType, }) {\n        let projectIds_ = projectIds || [];\n        if (projectNames) {\n            projectIds_ = [\n                ...(projectIds || []),\n                ...(await Promise.all(projectNames.map((name) => this.readProject({ projectName: name }).then((project) => project.id)))),\n            ];\n        }\n        const payload = {\n            id,\n            trace,\n            parent_run: parentRun,\n            run_type: runType,\n            session: projectIds_,\n            reference_example: referenceExampleIds,\n            start_time: startTime,\n            end_time: endTime,\n            error,\n            query,\n            filter,\n            trace_filter: traceFilter,\n            tree_filter: treeFilter,\n            is_root: isRoot,\n            data_source_type: dataSourceType,\n        };\n        // Remove undefined values from the payload\n        const filteredPayload = Object.fromEntries(Object.entries(payload).filter(([_, value]) => value !== undefined));\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), `${this.apiUrl}/runs/stats`, {\n            method: \"POST\",\n            headers: this.headers,\n            body: JSON.stringify(filteredPayload),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        const result = await response.json();\n        return result;\n    }\n    async shareRun(runId, { shareId } = {}) {\n        const data = {\n            run_id: runId,\n            share_token: shareId || uuid__WEBPACK_IMPORTED_MODULE_10__[\"default\"](),\n        };\n        (0,_utils_uuid_js__WEBPACK_IMPORTED_MODULE_4__.assertUuid)(runId);\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), `${this.apiUrl}/runs/${runId}/share`, {\n            method: \"PUT\",\n            headers: this.headers,\n            body: JSON.stringify(data),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        const result = await response.json();\n        if (result === null || !(\"share_token\" in result)) {\n            throw new Error(\"Invalid response from server\");\n        }\n        return `${this.getHostUrl()}/public/${result[\"share_token\"]}/r`;\n    }\n    async unshareRun(runId) {\n        (0,_utils_uuid_js__WEBPACK_IMPORTED_MODULE_4__.assertUuid)(runId);\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), `${this.apiUrl}/runs/${runId}/share`, {\n            method: \"DELETE\",\n            headers: this.headers,\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await (0,_utils_error_js__WEBPACK_IMPORTED_MODULE_7__.raiseForStatus)(response, \"unshare run\", true);\n    }\n    async readRunSharedLink(runId) {\n        (0,_utils_uuid_js__WEBPACK_IMPORTED_MODULE_4__.assertUuid)(runId);\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), `${this.apiUrl}/runs/${runId}/share`, {\n            method: \"GET\",\n            headers: this.headers,\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        const result = await response.json();\n        if (result === null || !(\"share_token\" in result)) {\n            return undefined;\n        }\n        return `${this.getHostUrl()}/public/${result[\"share_token\"]}/r`;\n    }\n    async listSharedRuns(shareToken, { runIds, } = {}) {\n        const queryParams = new URLSearchParams({\n            share_token: shareToken,\n        });\n        if (runIds !== undefined) {\n            for (const runId of runIds) {\n                queryParams.append(\"id\", runId);\n            }\n        }\n        (0,_utils_uuid_js__WEBPACK_IMPORTED_MODULE_4__.assertUuid)(shareToken);\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), `${this.apiUrl}/public/${shareToken}/runs${queryParams}`, {\n            method: \"GET\",\n            headers: this.headers,\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        const runs = await response.json();\n        return runs;\n    }\n    async readDatasetSharedSchema(datasetId, datasetName) {\n        if (!datasetId && !datasetName) {\n            throw new Error(\"Either datasetId or datasetName must be given\");\n        }\n        if (!datasetId) {\n            const dataset = await this.readDataset({ datasetName });\n            datasetId = dataset.id;\n        }\n        (0,_utils_uuid_js__WEBPACK_IMPORTED_MODULE_4__.assertUuid)(datasetId);\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), `${this.apiUrl}/datasets/${datasetId}/share`, {\n            method: \"GET\",\n            headers: this.headers,\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        const shareSchema = await response.json();\n        shareSchema.url = `${this.getHostUrl()}/public/${shareSchema.share_token}/d`;\n        return shareSchema;\n    }\n    async shareDataset(datasetId, datasetName) {\n        if (!datasetId && !datasetName) {\n            throw new Error(\"Either datasetId or datasetName must be given\");\n        }\n        if (!datasetId) {\n            const dataset = await this.readDataset({ datasetName });\n            datasetId = dataset.id;\n        }\n        const data = {\n            dataset_id: datasetId,\n        };\n        (0,_utils_uuid_js__WEBPACK_IMPORTED_MODULE_4__.assertUuid)(datasetId);\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), `${this.apiUrl}/datasets/${datasetId}/share`, {\n            method: \"PUT\",\n            headers: this.headers,\n            body: JSON.stringify(data),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        const shareSchema = await response.json();\n        shareSchema.url = `${this.getHostUrl()}/public/${shareSchema.share_token}/d`;\n        return shareSchema;\n    }\n    async unshareDataset(datasetId) {\n        (0,_utils_uuid_js__WEBPACK_IMPORTED_MODULE_4__.assertUuid)(datasetId);\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), `${this.apiUrl}/datasets/${datasetId}/share`, {\n            method: \"DELETE\",\n            headers: this.headers,\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await (0,_utils_error_js__WEBPACK_IMPORTED_MODULE_7__.raiseForStatus)(response, \"unshare dataset\", true);\n    }\n    async readSharedDataset(shareToken) {\n        (0,_utils_uuid_js__WEBPACK_IMPORTED_MODULE_4__.assertUuid)(shareToken);\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), `${this.apiUrl}/public/${shareToken}/datasets`, {\n            method: \"GET\",\n            headers: this.headers,\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        const dataset = await response.json();\n        return dataset;\n    }\n    /**\n     * Get shared examples.\n     *\n     * @param {string} shareToken The share token to get examples for. A share token is the UUID (or LangSmith URL, including UUID) generated when explicitly marking an example as public.\n     * @param {Object} [options] Additional options for listing the examples.\n     * @param {string[] | undefined} [options.exampleIds] A list of example IDs to filter by.\n     * @returns {Promise<Example[]>} The shared examples.\n     */\n    async listSharedExamples(shareToken, options) {\n        const params = {};\n        if (options?.exampleIds) {\n            params.id = options.exampleIds;\n        }\n        const urlParams = new URLSearchParams();\n        Object.entries(params).forEach(([key, value]) => {\n            if (Array.isArray(value)) {\n                value.forEach((v) => urlParams.append(key, v));\n            }\n            else {\n                urlParams.append(key, value);\n            }\n        });\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), `${this.apiUrl}/public/${shareToken}/examples?${urlParams.toString()}`, {\n            method: \"GET\",\n            headers: this.headers,\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        const result = await response.json();\n        if (!response.ok) {\n            if (\"detail\" in result) {\n                throw new Error(`Failed to list shared examples.\\nStatus: ${response.status}\\nMessage: ${result.detail.join(\"\\n\")}`);\n            }\n            throw new Error(`Failed to list shared examples: ${response.status} ${response.statusText}`);\n        }\n        return result.map((example) => ({\n            ...example,\n            _hostUrl: this.getHostUrl(),\n        }));\n    }\n    async createProject({ projectName, description = null, metadata = null, upsert = false, projectExtra = null, referenceDatasetId = null, }) {\n        const upsert_ = upsert ? `?upsert=true` : \"\";\n        const endpoint = `${this.apiUrl}/sessions${upsert_}`;\n        const extra = projectExtra || {};\n        if (metadata) {\n            extra[\"metadata\"] = metadata;\n        }\n        const body = {\n            name: projectName,\n            extra,\n            description,\n        };\n        if (referenceDatasetId !== null) {\n            body[\"reference_dataset_id\"] = referenceDatasetId;\n        }\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), endpoint, {\n            method: \"POST\",\n            headers: { ...this.headers, \"Content-Type\": \"application/json\" },\n            body: JSON.stringify(body),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await (0,_utils_error_js__WEBPACK_IMPORTED_MODULE_7__.raiseForStatus)(response, \"create project\");\n        const result = await response.json();\n        return result;\n    }\n    async updateProject(projectId, { name = null, description = null, metadata = null, projectExtra = null, endTime = null, }) {\n        const endpoint = `${this.apiUrl}/sessions/${projectId}`;\n        let extra = projectExtra;\n        if (metadata) {\n            extra = { ...(extra || {}), metadata };\n        }\n        const body = {\n            name,\n            extra,\n            description,\n            end_time: endTime ? new Date(endTime).toISOString() : null,\n        };\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), endpoint, {\n            method: \"PATCH\",\n            headers: { ...this.headers, \"Content-Type\": \"application/json\" },\n            body: JSON.stringify(body),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await (0,_utils_error_js__WEBPACK_IMPORTED_MODULE_7__.raiseForStatus)(response, \"update project\");\n        const result = await response.json();\n        return result;\n    }\n    async hasProject({ projectId, projectName, }) {\n        // TODO: Add a head request\n        let path = \"/sessions\";\n        const params = new URLSearchParams();\n        if (projectId !== undefined && projectName !== undefined) {\n            throw new Error(\"Must provide either projectName or projectId, not both\");\n        }\n        else if (projectId !== undefined) {\n            (0,_utils_uuid_js__WEBPACK_IMPORTED_MODULE_4__.assertUuid)(projectId);\n            path += `/${projectId}`;\n        }\n        else if (projectName !== undefined) {\n            params.append(\"name\", projectName);\n        }\n        else {\n            throw new Error(\"Must provide projectName or projectId\");\n        }\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), `${this.apiUrl}${path}?${params}`, {\n            method: \"GET\",\n            headers: this.headers,\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        // consume the response body to release the connection\n        // https://undici.nodejs.org/#/?id=garbage-collection\n        try {\n            const result = await response.json();\n            if (!response.ok) {\n                return false;\n            }\n            // If it's OK and we're querying by name, need to check the list is not empty\n            if (Array.isArray(result)) {\n                return result.length > 0;\n            }\n            // projectId querying\n            return true;\n        }\n        catch (e) {\n            return false;\n        }\n    }\n    async readProject({ projectId, projectName, includeStats, }) {\n        let path = \"/sessions\";\n        const params = new URLSearchParams();\n        if (projectId !== undefined && projectName !== undefined) {\n            throw new Error(\"Must provide either projectName or projectId, not both\");\n        }\n        else if (projectId !== undefined) {\n            (0,_utils_uuid_js__WEBPACK_IMPORTED_MODULE_4__.assertUuid)(projectId);\n            path += `/${projectId}`;\n        }\n        else if (projectName !== undefined) {\n            params.append(\"name\", projectName);\n        }\n        else {\n            throw new Error(\"Must provide projectName or projectId\");\n        }\n        if (includeStats !== undefined) {\n            params.append(\"include_stats\", includeStats.toString());\n        }\n        const response = await this._get(path, params);\n        let result;\n        if (Array.isArray(response)) {\n            if (response.length === 0) {\n                throw new Error(`Project[id=${projectId}, name=${projectName}] not found`);\n            }\n            result = response[0];\n        }\n        else {\n            result = response;\n        }\n        return result;\n    }\n    async getProjectUrl({ projectId, projectName, }) {\n        if (projectId === undefined && projectName === undefined) {\n            throw new Error(\"Must provide either projectName or projectId\");\n        }\n        const project = await this.readProject({ projectId, projectName });\n        const tenantId = await this._getTenantId();\n        return `${this.getHostUrl()}/o/${tenantId}/projects/p/${project.id}`;\n    }\n    async getDatasetUrl({ datasetId, datasetName, }) {\n        if (datasetId === undefined && datasetName === undefined) {\n            throw new Error(\"Must provide either datasetName or datasetId\");\n        }\n        const dataset = await this.readDataset({ datasetId, datasetName });\n        const tenantId = await this._getTenantId();\n        return `${this.getHostUrl()}/o/${tenantId}/datasets/${dataset.id}`;\n    }\n    async _getTenantId() {\n        if (this._tenantId !== null) {\n            return this._tenantId;\n        }\n        const queryParams = new URLSearchParams({ limit: \"1\" });\n        for await (const projects of this._getPaginated(\"/sessions\", queryParams)) {\n            this._tenantId = projects[0].tenant_id;\n            return projects[0].tenant_id;\n        }\n        throw new Error(\"No projects found to resolve tenant.\");\n    }\n    async *listProjects({ projectIds, name, nameContains, referenceDatasetId, referenceDatasetName, referenceFree, metadata, } = {}) {\n        const params = new URLSearchParams();\n        if (projectIds !== undefined) {\n            for (const projectId of projectIds) {\n                params.append(\"id\", projectId);\n            }\n        }\n        if (name !== undefined) {\n            params.append(\"name\", name);\n        }\n        if (nameContains !== undefined) {\n            params.append(\"name_contains\", nameContains);\n        }\n        if (referenceDatasetId !== undefined) {\n            params.append(\"reference_dataset\", referenceDatasetId);\n        }\n        else if (referenceDatasetName !== undefined) {\n            const dataset = await this.readDataset({\n                datasetName: referenceDatasetName,\n            });\n            params.append(\"reference_dataset\", dataset.id);\n        }\n        if (referenceFree !== undefined) {\n            params.append(\"reference_free\", referenceFree.toString());\n        }\n        if (metadata !== undefined) {\n            params.append(\"metadata\", JSON.stringify(metadata));\n        }\n        for await (const projects of this._getPaginated(\"/sessions\", params)) {\n            yield* projects;\n        }\n    }\n    async deleteProject({ projectId, projectName, }) {\n        let projectId_;\n        if (projectId === undefined && projectName === undefined) {\n            throw new Error(\"Must provide projectName or projectId\");\n        }\n        else if (projectId !== undefined && projectName !== undefined) {\n            throw new Error(\"Must provide either projectName or projectId, not both\");\n        }\n        else if (projectId === undefined) {\n            projectId_ = (await this.readProject({ projectName })).id;\n        }\n        else {\n            projectId_ = projectId;\n        }\n        (0,_utils_uuid_js__WEBPACK_IMPORTED_MODULE_4__.assertUuid)(projectId_);\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), `${this.apiUrl}/sessions/${projectId_}`, {\n            method: \"DELETE\",\n            headers: this.headers,\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await (0,_utils_error_js__WEBPACK_IMPORTED_MODULE_7__.raiseForStatus)(response, `delete session ${projectId_} (${projectName})`, true);\n    }\n    async uploadCsv({ csvFile, fileName, inputKeys, outputKeys, description, dataType, name, }) {\n        const url = `${this.apiUrl}/datasets/upload`;\n        const formData = new FormData();\n        formData.append(\"file\", csvFile, fileName);\n        inputKeys.forEach((key) => {\n            formData.append(\"input_keys\", key);\n        });\n        outputKeys.forEach((key) => {\n            formData.append(\"output_keys\", key);\n        });\n        if (description) {\n            formData.append(\"description\", description);\n        }\n        if (dataType) {\n            formData.append(\"data_type\", dataType);\n        }\n        if (name) {\n            formData.append(\"name\", name);\n        }\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), url, {\n            method: \"POST\",\n            headers: this.headers,\n            body: formData,\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await (0,_utils_error_js__WEBPACK_IMPORTED_MODULE_7__.raiseForStatus)(response, \"upload CSV\");\n        const result = await response.json();\n        return result;\n    }\n    async createDataset(name, { description, dataType, inputsSchema, outputsSchema, metadata, } = {}) {\n        const body = {\n            name,\n            description,\n            extra: metadata ? { metadata } : undefined,\n        };\n        if (dataType) {\n            body.data_type = dataType;\n        }\n        if (inputsSchema) {\n            body.inputs_schema_definition = inputsSchema;\n        }\n        if (outputsSchema) {\n            body.outputs_schema_definition = outputsSchema;\n        }\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), `${this.apiUrl}/datasets`, {\n            method: \"POST\",\n            headers: { ...this.headers, \"Content-Type\": \"application/json\" },\n            body: JSON.stringify(body),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await (0,_utils_error_js__WEBPACK_IMPORTED_MODULE_7__.raiseForStatus)(response, \"create dataset\");\n        const result = await response.json();\n        return result;\n    }\n    async readDataset({ datasetId, datasetName, }) {\n        let path = \"/datasets\";\n        // limit to 1 result\n        const params = new URLSearchParams({ limit: \"1\" });\n        if (datasetId !== undefined && datasetName !== undefined) {\n            throw new Error(\"Must provide either datasetName or datasetId, not both\");\n        }\n        else if (datasetId !== undefined) {\n            (0,_utils_uuid_js__WEBPACK_IMPORTED_MODULE_4__.assertUuid)(datasetId);\n            path += `/${datasetId}`;\n        }\n        else if (datasetName !== undefined) {\n            params.append(\"name\", datasetName);\n        }\n        else {\n            throw new Error(\"Must provide datasetName or datasetId\");\n        }\n        const response = await this._get(path, params);\n        let result;\n        if (Array.isArray(response)) {\n            if (response.length === 0) {\n                throw new Error(`Dataset[id=${datasetId}, name=${datasetName}] not found`);\n            }\n            result = response[0];\n        }\n        else {\n            result = response;\n        }\n        return result;\n    }\n    async hasDataset({ datasetId, datasetName, }) {\n        try {\n            await this.readDataset({ datasetId, datasetName });\n            return true;\n        }\n        catch (e) {\n            if (\n            // eslint-disable-next-line no-instanceof/no-instanceof\n            e instanceof Error &&\n                e.message.toLocaleLowerCase().includes(\"not found\")) {\n                return false;\n            }\n            throw e;\n        }\n    }\n    async diffDatasetVersions({ datasetId, datasetName, fromVersion, toVersion, }) {\n        let datasetId_ = datasetId;\n        if (datasetId_ === undefined && datasetName === undefined) {\n            throw new Error(\"Must provide either datasetName or datasetId\");\n        }\n        else if (datasetId_ !== undefined && datasetName !== undefined) {\n            throw new Error(\"Must provide either datasetName or datasetId, not both\");\n        }\n        else if (datasetId_ === undefined) {\n            const dataset = await this.readDataset({ datasetName });\n            datasetId_ = dataset.id;\n        }\n        const urlParams = new URLSearchParams({\n            from_version: typeof fromVersion === \"string\"\n                ? fromVersion\n                : fromVersion.toISOString(),\n            to_version: typeof toVersion === \"string\" ? toVersion : toVersion.toISOString(),\n        });\n        const response = await this._get(`/datasets/${datasetId_}/versions/diff`, urlParams);\n        return response;\n    }\n    async readDatasetOpenaiFinetuning({ datasetId, datasetName, }) {\n        const path = \"/datasets\";\n        if (datasetId !== undefined) {\n            // do nothing\n        }\n        else if (datasetName !== undefined) {\n            datasetId = (await this.readDataset({ datasetName })).id;\n        }\n        else {\n            throw new Error(\"Must provide datasetName or datasetId\");\n        }\n        const response = await this._getResponse(`${path}/${datasetId}/openai_ft`);\n        const datasetText = await response.text();\n        const dataset = datasetText\n            .trim()\n            .split(\"\\n\")\n            .map((line) => JSON.parse(line));\n        return dataset;\n    }\n    async *listDatasets({ limit = 100, offset = 0, datasetIds, datasetName, datasetNameContains, metadata, } = {}) {\n        const path = \"/datasets\";\n        const params = new URLSearchParams({\n            limit: limit.toString(),\n            offset: offset.toString(),\n        });\n        if (datasetIds !== undefined) {\n            for (const id_ of datasetIds) {\n                params.append(\"id\", id_);\n            }\n        }\n        if (datasetName !== undefined) {\n            params.append(\"name\", datasetName);\n        }\n        if (datasetNameContains !== undefined) {\n            params.append(\"name_contains\", datasetNameContains);\n        }\n        if (metadata !== undefined) {\n            params.append(\"metadata\", JSON.stringify(metadata));\n        }\n        for await (const datasets of this._getPaginated(path, params)) {\n            yield* datasets;\n        }\n    }\n    /**\n     * Update a dataset\n     * @param props The dataset details to update\n     * @returns The updated dataset\n     */\n    async updateDataset(props) {\n        const { datasetId, datasetName, ...update } = props;\n        if (!datasetId && !datasetName) {\n            throw new Error(\"Must provide either datasetName or datasetId\");\n        }\n        const _datasetId = datasetId ?? (await this.readDataset({ datasetName })).id;\n        (0,_utils_uuid_js__WEBPACK_IMPORTED_MODULE_4__.assertUuid)(_datasetId);\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), `${this.apiUrl}/datasets/${_datasetId}`, {\n            method: \"PATCH\",\n            headers: { ...this.headers, \"Content-Type\": \"application/json\" },\n            body: JSON.stringify(update),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await (0,_utils_error_js__WEBPACK_IMPORTED_MODULE_7__.raiseForStatus)(response, \"update dataset\");\n        return (await response.json());\n    }\n    async deleteDataset({ datasetId, datasetName, }) {\n        let path = \"/datasets\";\n        let datasetId_ = datasetId;\n        if (datasetId !== undefined && datasetName !== undefined) {\n            throw new Error(\"Must provide either datasetName or datasetId, not both\");\n        }\n        else if (datasetName !== undefined) {\n            const dataset = await this.readDataset({ datasetName });\n            datasetId_ = dataset.id;\n        }\n        if (datasetId_ !== undefined) {\n            (0,_utils_uuid_js__WEBPACK_IMPORTED_MODULE_4__.assertUuid)(datasetId_);\n            path += `/${datasetId_}`;\n        }\n        else {\n            throw new Error(\"Must provide datasetName or datasetId\");\n        }\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), this.apiUrl + path, {\n            method: \"DELETE\",\n            headers: this.headers,\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await (0,_utils_error_js__WEBPACK_IMPORTED_MODULE_7__.raiseForStatus)(response, `delete ${path}`);\n        await response.json();\n    }\n    async indexDataset({ datasetId, datasetName, tag, }) {\n        let datasetId_ = datasetId;\n        if (!datasetId_ && !datasetName) {\n            throw new Error(\"Must provide either datasetName or datasetId\");\n        }\n        else if (datasetId_ && datasetName) {\n            throw new Error(\"Must provide either datasetName or datasetId, not both\");\n        }\n        else if (!datasetId_) {\n            const dataset = await this.readDataset({ datasetName });\n            datasetId_ = dataset.id;\n        }\n        (0,_utils_uuid_js__WEBPACK_IMPORTED_MODULE_4__.assertUuid)(datasetId_);\n        const data = {\n            tag: tag,\n        };\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), `${this.apiUrl}/datasets/${datasetId_}/index`, {\n            method: \"POST\",\n            headers: { ...this.headers, \"Content-Type\": \"application/json\" },\n            body: JSON.stringify(data),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await (0,_utils_error_js__WEBPACK_IMPORTED_MODULE_7__.raiseForStatus)(response, \"index dataset\");\n        await response.json();\n    }\n    /**\n     * Lets you run a similarity search query on a dataset.\n     *\n     * Requires the dataset to be indexed. Please see the `indexDataset` method to set up indexing.\n     *\n     * @param inputs      The input on which to run the similarity search. Must have the\n     *                    same schema as the dataset.\n     *\n     * @param datasetId   The dataset to search for similar examples.\n     *\n     * @param limit       The maximum number of examples to return. Will return the top `limit` most\n     *                    similar examples in order of most similar to least similar. If no similar\n     *                    examples are found, random examples will be returned.\n     *\n     * @param filter      A filter string to apply to the search. Only examples will be returned that\n     *                    match the filter string. Some examples of filters\n     *\n     *                    - eq(metadata.mykey, \"value\")\n     *                    - and(neq(metadata.my.nested.key, \"value\"), neq(metadata.mykey, \"value\"))\n     *                    - or(eq(metadata.mykey, \"value\"), eq(metadata.mykey, \"othervalue\"))\n     *\n     * @returns           A list of similar examples.\n     *\n     *\n     * @example\n     * dataset_id = \"123e4567-e89b-12d3-a456-************\"\n     * inputs = {\"text\": \"How many people live in Berlin?\"}\n     * limit = 5\n     * examples = await client.similarExamples(inputs, dataset_id, limit)\n     */\n    async similarExamples(inputs, datasetId, limit, { filter, } = {}) {\n        const data = {\n            limit: limit,\n            inputs: inputs,\n        };\n        if (filter !== undefined) {\n            data[\"filter\"] = filter;\n        }\n        (0,_utils_uuid_js__WEBPACK_IMPORTED_MODULE_4__.assertUuid)(datasetId);\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), `${this.apiUrl}/datasets/${datasetId}/search`, {\n            method: \"POST\",\n            headers: { ...this.headers, \"Content-Type\": \"application/json\" },\n            body: JSON.stringify(data),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await (0,_utils_error_js__WEBPACK_IMPORTED_MODULE_7__.raiseForStatus)(response, \"fetch similar examples\");\n        const result = await response.json();\n        return result[\"examples\"];\n    }\n    async createExample(inputs, outputs, { datasetId, datasetName, createdAt, exampleId, metadata, split, sourceRunId, }) {\n        let datasetId_ = datasetId;\n        if (datasetId_ === undefined && datasetName === undefined) {\n            throw new Error(\"Must provide either datasetName or datasetId\");\n        }\n        else if (datasetId_ !== undefined && datasetName !== undefined) {\n            throw new Error(\"Must provide either datasetName or datasetId, not both\");\n        }\n        else if (datasetId_ === undefined) {\n            const dataset = await this.readDataset({ datasetName });\n            datasetId_ = dataset.id;\n        }\n        const createdAt_ = createdAt || new Date();\n        const data = {\n            dataset_id: datasetId_,\n            inputs,\n            outputs,\n            created_at: createdAt_?.toISOString(),\n            id: exampleId,\n            metadata,\n            split,\n            source_run_id: sourceRunId,\n        };\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), `${this.apiUrl}/examples`, {\n            method: \"POST\",\n            headers: { ...this.headers, \"Content-Type\": \"application/json\" },\n            body: JSON.stringify(data),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await (0,_utils_error_js__WEBPACK_IMPORTED_MODULE_7__.raiseForStatus)(response, \"create example\");\n        const result = await response.json();\n        return result;\n    }\n    async createExamples(props) {\n        const { inputs, outputs, metadata, sourceRunIds, exampleIds, datasetId, datasetName, } = props;\n        let datasetId_ = datasetId;\n        if (datasetId_ === undefined && datasetName === undefined) {\n            throw new Error(\"Must provide either datasetName or datasetId\");\n        }\n        else if (datasetId_ !== undefined && datasetName !== undefined) {\n            throw new Error(\"Must provide either datasetName or datasetId, not both\");\n        }\n        else if (datasetId_ === undefined) {\n            const dataset = await this.readDataset({ datasetName });\n            datasetId_ = dataset.id;\n        }\n        const formattedExamples = inputs.map((input, idx) => {\n            return {\n                dataset_id: datasetId_,\n                inputs: input,\n                outputs: outputs ? outputs[idx] : undefined,\n                metadata: metadata ? metadata[idx] : undefined,\n                split: props.splits ? props.splits[idx] : undefined,\n                id: exampleIds ? exampleIds[idx] : undefined,\n                source_run_id: sourceRunIds ? sourceRunIds[idx] : undefined,\n            };\n        });\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), `${this.apiUrl}/examples/bulk`, {\n            method: \"POST\",\n            headers: { ...this.headers, \"Content-Type\": \"application/json\" },\n            body: JSON.stringify(formattedExamples),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await (0,_utils_error_js__WEBPACK_IMPORTED_MODULE_7__.raiseForStatus)(response, \"create examples\");\n        const result = await response.json();\n        return result;\n    }\n    async createLLMExample(input, generation, options) {\n        return this.createExample({ input }, { output: generation }, options);\n    }\n    async createChatExample(input, generations, options) {\n        const finalInput = input.map((message) => {\n            if ((0,_utils_messages_js__WEBPACK_IMPORTED_MODULE_1__.isLangChainMessage)(message)) {\n                return (0,_utils_messages_js__WEBPACK_IMPORTED_MODULE_1__.convertLangChainMessageToExample)(message);\n            }\n            return message;\n        });\n        const finalOutput = (0,_utils_messages_js__WEBPACK_IMPORTED_MODULE_1__.isLangChainMessage)(generations)\n            ? (0,_utils_messages_js__WEBPACK_IMPORTED_MODULE_1__.convertLangChainMessageToExample)(generations)\n            : generations;\n        return this.createExample({ input: finalInput }, { output: finalOutput }, options);\n    }\n    async readExample(exampleId) {\n        (0,_utils_uuid_js__WEBPACK_IMPORTED_MODULE_4__.assertUuid)(exampleId);\n        const path = `/examples/${exampleId}`;\n        return await this._get(path);\n    }\n    async *listExamples({ datasetId, datasetName, exampleIds, asOf, splits, inlineS3Urls, metadata, limit, offset, filter, } = {}) {\n        let datasetId_;\n        if (datasetId !== undefined && datasetName !== undefined) {\n            throw new Error(\"Must provide either datasetName or datasetId, not both\");\n        }\n        else if (datasetId !== undefined) {\n            datasetId_ = datasetId;\n        }\n        else if (datasetName !== undefined) {\n            const dataset = await this.readDataset({ datasetName });\n            datasetId_ = dataset.id;\n        }\n        else {\n            throw new Error(\"Must provide a datasetName or datasetId\");\n        }\n        const params = new URLSearchParams({ dataset: datasetId_ });\n        const dataset_version = asOf\n            ? typeof asOf === \"string\"\n                ? asOf\n                : asOf?.toISOString()\n            : undefined;\n        if (dataset_version) {\n            params.append(\"as_of\", dataset_version);\n        }\n        const inlineS3Urls_ = inlineS3Urls ?? true;\n        params.append(\"inline_s3_urls\", inlineS3Urls_.toString());\n        if (exampleIds !== undefined) {\n            for (const id_ of exampleIds) {\n                params.append(\"id\", id_);\n            }\n        }\n        if (splits !== undefined) {\n            for (const split of splits) {\n                params.append(\"splits\", split);\n            }\n        }\n        if (metadata !== undefined) {\n            const serializedMetadata = JSON.stringify(metadata);\n            params.append(\"metadata\", serializedMetadata);\n        }\n        if (limit !== undefined) {\n            params.append(\"limit\", limit.toString());\n        }\n        if (offset !== undefined) {\n            params.append(\"offset\", offset.toString());\n        }\n        if (filter !== undefined) {\n            params.append(\"filter\", filter);\n        }\n        let i = 0;\n        for await (const examples of this._getPaginated(\"/examples\", params)) {\n            for (const example of examples) {\n                yield example;\n                i++;\n            }\n            if (limit !== undefined && i >= limit) {\n                break;\n            }\n        }\n    }\n    async deleteExample(exampleId) {\n        (0,_utils_uuid_js__WEBPACK_IMPORTED_MODULE_4__.assertUuid)(exampleId);\n        const path = `/examples/${exampleId}`;\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), this.apiUrl + path, {\n            method: \"DELETE\",\n            headers: this.headers,\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await (0,_utils_error_js__WEBPACK_IMPORTED_MODULE_7__.raiseForStatus)(response, `delete ${path}`);\n        await response.json();\n    }\n    async updateExample(exampleId, update) {\n        (0,_utils_uuid_js__WEBPACK_IMPORTED_MODULE_4__.assertUuid)(exampleId);\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), `${this.apiUrl}/examples/${exampleId}`, {\n            method: \"PATCH\",\n            headers: { ...this.headers, \"Content-Type\": \"application/json\" },\n            body: JSON.stringify(update),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await (0,_utils_error_js__WEBPACK_IMPORTED_MODULE_7__.raiseForStatus)(response, \"update example\");\n        const result = await response.json();\n        return result;\n    }\n    async updateExamples(update) {\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), `${this.apiUrl}/examples/bulk`, {\n            method: \"PATCH\",\n            headers: { ...this.headers, \"Content-Type\": \"application/json\" },\n            body: JSON.stringify(update),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await (0,_utils_error_js__WEBPACK_IMPORTED_MODULE_7__.raiseForStatus)(response, \"update examples\");\n        const result = await response.json();\n        return result;\n    }\n    async listDatasetSplits({ datasetId, datasetName, asOf, }) {\n        let datasetId_;\n        if (datasetId === undefined && datasetName === undefined) {\n            throw new Error(\"Must provide dataset name or ID\");\n        }\n        else if (datasetId !== undefined && datasetName !== undefined) {\n            throw new Error(\"Must provide either datasetName or datasetId, not both\");\n        }\n        else if (datasetId === undefined) {\n            const dataset = await this.readDataset({ datasetName });\n            datasetId_ = dataset.id;\n        }\n        else {\n            datasetId_ = datasetId;\n        }\n        (0,_utils_uuid_js__WEBPACK_IMPORTED_MODULE_4__.assertUuid)(datasetId_);\n        const params = new URLSearchParams();\n        const dataset_version = asOf\n            ? typeof asOf === \"string\"\n                ? asOf\n                : asOf?.toISOString()\n            : undefined;\n        if (dataset_version) {\n            params.append(\"as_of\", dataset_version);\n        }\n        const response = await this._get(`/datasets/${datasetId_}/splits`, params);\n        return response;\n    }\n    async updateDatasetSplits({ datasetId, datasetName, splitName, exampleIds, remove = false, }) {\n        let datasetId_;\n        if (datasetId === undefined && datasetName === undefined) {\n            throw new Error(\"Must provide dataset name or ID\");\n        }\n        else if (datasetId !== undefined && datasetName !== undefined) {\n            throw new Error(\"Must provide either datasetName or datasetId, not both\");\n        }\n        else if (datasetId === undefined) {\n            const dataset = await this.readDataset({ datasetName });\n            datasetId_ = dataset.id;\n        }\n        else {\n            datasetId_ = datasetId;\n        }\n        (0,_utils_uuid_js__WEBPACK_IMPORTED_MODULE_4__.assertUuid)(datasetId_);\n        const data = {\n            split_name: splitName,\n            examples: exampleIds.map((id) => {\n                (0,_utils_uuid_js__WEBPACK_IMPORTED_MODULE_4__.assertUuid)(id);\n                return id;\n            }),\n            remove,\n        };\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), `${this.apiUrl}/datasets/${datasetId_}/splits`, {\n            method: \"PUT\",\n            headers: { ...this.headers, \"Content-Type\": \"application/json\" },\n            body: JSON.stringify(data),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await (0,_utils_error_js__WEBPACK_IMPORTED_MODULE_7__.raiseForStatus)(response, \"update dataset splits\", true);\n    }\n    /**\n     * @deprecated This method is deprecated and will be removed in future LangSmith versions, use `evaluate` from `langsmith/evaluation` instead.\n     */\n    async evaluateRun(run, evaluator, { sourceInfo, loadChildRuns, referenceExample, } = { loadChildRuns: false }) {\n        (0,_utils_warn_js__WEBPACK_IMPORTED_MODULE_5__.warnOnce)(\"This method is deprecated and will be removed in future LangSmith versions, use `evaluate` from `langsmith/evaluation` instead.\");\n        let run_;\n        if (typeof run === \"string\") {\n            run_ = await this.readRun(run, { loadChildRuns });\n        }\n        else if (typeof run === \"object\" && \"id\" in run) {\n            run_ = run;\n        }\n        else {\n            throw new Error(`Invalid run type: ${typeof run}`);\n        }\n        if (run_.reference_example_id !== null &&\n            run_.reference_example_id !== undefined) {\n            referenceExample = await this.readExample(run_.reference_example_id);\n        }\n        const feedbackResult = await evaluator.evaluateRun(run_, referenceExample);\n        const [_, feedbacks] = await this._logEvaluationFeedback(feedbackResult, run_, sourceInfo);\n        return feedbacks[0];\n    }\n    async createFeedback(runId, key, { score, value, correction, comment, sourceInfo, feedbackSourceType = \"api\", sourceRunId, feedbackId, feedbackConfig, projectId, comparativeExperimentId, }) {\n        if (!runId && !projectId) {\n            throw new Error(\"One of runId or projectId must be provided\");\n        }\n        if (runId && projectId) {\n            throw new Error(\"Only one of runId or projectId can be provided\");\n        }\n        const feedback_source = {\n            type: feedbackSourceType ?? \"api\",\n            metadata: sourceInfo ?? {},\n        };\n        if (sourceRunId !== undefined &&\n            feedback_source?.metadata !== undefined &&\n            !feedback_source.metadata[\"__run\"]) {\n            feedback_source.metadata[\"__run\"] = { run_id: sourceRunId };\n        }\n        if (feedback_source?.metadata !== undefined &&\n            feedback_source.metadata[\"__run\"]?.run_id !== undefined) {\n            (0,_utils_uuid_js__WEBPACK_IMPORTED_MODULE_4__.assertUuid)(feedback_source.metadata[\"__run\"].run_id);\n        }\n        const feedback = {\n            id: feedbackId ?? uuid__WEBPACK_IMPORTED_MODULE_10__[\"default\"](),\n            run_id: runId,\n            key,\n            score,\n            value,\n            correction,\n            comment,\n            feedback_source: feedback_source,\n            comparative_experiment_id: comparativeExperimentId,\n            feedbackConfig,\n            session_id: projectId,\n        };\n        const url = `${this.apiUrl}/feedback`;\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), url, {\n            method: \"POST\",\n            headers: { ...this.headers, \"Content-Type\": \"application/json\" },\n            body: JSON.stringify(feedback),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await (0,_utils_error_js__WEBPACK_IMPORTED_MODULE_7__.raiseForStatus)(response, \"create feedback\", true);\n        return feedback;\n    }\n    async updateFeedback(feedbackId, { score, value, correction, comment, }) {\n        const feedbackUpdate = {};\n        if (score !== undefined && score !== null) {\n            feedbackUpdate[\"score\"] = score;\n        }\n        if (value !== undefined && value !== null) {\n            feedbackUpdate[\"value\"] = value;\n        }\n        if (correction !== undefined && correction !== null) {\n            feedbackUpdate[\"correction\"] = correction;\n        }\n        if (comment !== undefined && comment !== null) {\n            feedbackUpdate[\"comment\"] = comment;\n        }\n        (0,_utils_uuid_js__WEBPACK_IMPORTED_MODULE_4__.assertUuid)(feedbackId);\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), `${this.apiUrl}/feedback/${feedbackId}`, {\n            method: \"PATCH\",\n            headers: { ...this.headers, \"Content-Type\": \"application/json\" },\n            body: JSON.stringify(feedbackUpdate),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await (0,_utils_error_js__WEBPACK_IMPORTED_MODULE_7__.raiseForStatus)(response, \"update feedback\", true);\n    }\n    async readFeedback(feedbackId) {\n        (0,_utils_uuid_js__WEBPACK_IMPORTED_MODULE_4__.assertUuid)(feedbackId);\n        const path = `/feedback/${feedbackId}`;\n        const response = await this._get(path);\n        return response;\n    }\n    async deleteFeedback(feedbackId) {\n        (0,_utils_uuid_js__WEBPACK_IMPORTED_MODULE_4__.assertUuid)(feedbackId);\n        const path = `/feedback/${feedbackId}`;\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), this.apiUrl + path, {\n            method: \"DELETE\",\n            headers: this.headers,\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await (0,_utils_error_js__WEBPACK_IMPORTED_MODULE_7__.raiseForStatus)(response, `delete ${path}`);\n        await response.json();\n    }\n    async *listFeedback({ runIds, feedbackKeys, feedbackSourceTypes, } = {}) {\n        const queryParams = new URLSearchParams();\n        if (runIds) {\n            queryParams.append(\"run\", runIds.join(\",\"));\n        }\n        if (feedbackKeys) {\n            for (const key of feedbackKeys) {\n                queryParams.append(\"key\", key);\n            }\n        }\n        if (feedbackSourceTypes) {\n            for (const type of feedbackSourceTypes) {\n                queryParams.append(\"source\", type);\n            }\n        }\n        for await (const feedbacks of this._getPaginated(\"/feedback\", queryParams)) {\n            yield* feedbacks;\n        }\n    }\n    /**\n     * Creates a presigned feedback token and URL.\n     *\n     * The token can be used to authorize feedback metrics without\n     * needing an API key. This is useful for giving browser-based\n     * applications the ability to submit feedback without needing\n     * to expose an API key.\n     *\n     * @param runId - The ID of the run.\n     * @param feedbackKey - The feedback key.\n     * @param options - Additional options for the token.\n     * @param options.expiration - The expiration time for the token.\n     *\n     * @returns A promise that resolves to a FeedbackIngestToken.\n     */\n    async createPresignedFeedbackToken(runId, feedbackKey, { expiration, feedbackConfig, } = {}) {\n        const body = {\n            run_id: runId,\n            feedback_key: feedbackKey,\n            feedback_config: feedbackConfig,\n        };\n        if (expiration) {\n            if (typeof expiration === \"string\") {\n                body[\"expires_at\"] = expiration;\n            }\n            else if (expiration?.hours || expiration?.minutes || expiration?.days) {\n                body[\"expires_in\"] = expiration;\n            }\n        }\n        else {\n            body[\"expires_in\"] = {\n                hours: 3,\n            };\n        }\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), `${this.apiUrl}/feedback/tokens`, {\n            method: \"POST\",\n            headers: { ...this.headers, \"Content-Type\": \"application/json\" },\n            body: JSON.stringify(body),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        const result = await response.json();\n        return result;\n    }\n    async createComparativeExperiment({ name, experimentIds, referenceDatasetId, createdAt, description, metadata, id, }) {\n        if (experimentIds.length === 0) {\n            throw new Error(\"At least one experiment is required\");\n        }\n        if (!referenceDatasetId) {\n            referenceDatasetId = (await this.readProject({\n                projectId: experimentIds[0],\n            })).reference_dataset_id;\n        }\n        if (!referenceDatasetId == null) {\n            throw new Error(\"A reference dataset is required\");\n        }\n        const body = {\n            id,\n            name,\n            experiment_ids: experimentIds,\n            reference_dataset_id: referenceDatasetId,\n            description,\n            created_at: (createdAt ?? new Date())?.toISOString(),\n            extra: {},\n        };\n        if (metadata)\n            body.extra[\"metadata\"] = metadata;\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), `${this.apiUrl}/datasets/comparative`, {\n            method: \"POST\",\n            headers: { ...this.headers, \"Content-Type\": \"application/json\" },\n            body: JSON.stringify(body),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        return await response.json();\n    }\n    /**\n     * Retrieves a list of presigned feedback tokens for a given run ID.\n     * @param runId The ID of the run.\n     * @returns An async iterable of FeedbackIngestToken objects.\n     */\n    async *listPresignedFeedbackTokens(runId) {\n        (0,_utils_uuid_js__WEBPACK_IMPORTED_MODULE_4__.assertUuid)(runId);\n        const params = new URLSearchParams({ run_id: runId });\n        for await (const tokens of this._getPaginated(\"/feedback/tokens\", params)) {\n            yield* tokens;\n        }\n    }\n    _selectEvalResults(results) {\n        let results_;\n        if (\"results\" in results) {\n            results_ = results.results;\n        }\n        else {\n            results_ = [results];\n        }\n        return results_;\n    }\n    async _logEvaluationFeedback(evaluatorResponse, run, sourceInfo) {\n        const evalResults = this._selectEvalResults(evaluatorResponse);\n        const feedbacks = [];\n        for (const res of evalResults) {\n            let sourceInfo_ = sourceInfo || {};\n            if (res.evaluatorInfo) {\n                sourceInfo_ = { ...res.evaluatorInfo, ...sourceInfo_ };\n            }\n            let runId_ = null;\n            if (res.targetRunId) {\n                runId_ = res.targetRunId;\n            }\n            else if (run) {\n                runId_ = run.id;\n            }\n            feedbacks.push(await this.createFeedback(runId_, res.key, {\n                score: res.score,\n                value: res.value,\n                comment: res.comment,\n                correction: res.correction,\n                sourceInfo: sourceInfo_,\n                sourceRunId: res.sourceRunId,\n                feedbackConfig: res.feedbackConfig,\n                feedbackSourceType: \"model\",\n            }));\n        }\n        return [evalResults, feedbacks];\n    }\n    async logEvaluationFeedback(evaluatorResponse, run, sourceInfo) {\n        const [results] = await this._logEvaluationFeedback(evaluatorResponse, run, sourceInfo);\n        return results;\n    }\n    /**\n     * API for managing annotation queues\n     */\n    /**\n     * List the annotation queues on the LangSmith API.\n     * @param options - The options for listing annotation queues\n     * @param options.queueIds - The IDs of the queues to filter by\n     * @param options.name - The name of the queue to filter by\n     * @param options.nameContains - The substring that the queue name should contain\n     * @param options.limit - The maximum number of queues to return\n     * @returns An iterator of AnnotationQueue objects\n     */\n    async *listAnnotationQueues(options = {}) {\n        const { queueIds, name, nameContains, limit } = options;\n        const params = new URLSearchParams();\n        if (queueIds) {\n            queueIds.forEach((id, i) => {\n                (0,_utils_uuid_js__WEBPACK_IMPORTED_MODULE_4__.assertUuid)(id, `queueIds[${i}]`);\n                params.append(\"ids\", id);\n            });\n        }\n        if (name)\n            params.append(\"name\", name);\n        if (nameContains)\n            params.append(\"name_contains\", nameContains);\n        params.append(\"limit\", (limit !== undefined ? Math.min(limit, 100) : 100).toString());\n        let count = 0;\n        for await (const queues of this._getPaginated(\"/annotation-queues\", params)) {\n            yield* queues;\n            count++;\n            if (limit !== undefined && count >= limit)\n                break;\n        }\n    }\n    /**\n     * Create an annotation queue on the LangSmith API.\n     * @param options - The options for creating an annotation queue\n     * @param options.name - The name of the annotation queue\n     * @param options.description - The description of the annotation queue\n     * @param options.queueId - The ID of the annotation queue\n     * @returns The created AnnotationQueue object\n     */\n    async createAnnotationQueue(options) {\n        const { name, description, queueId } = options;\n        const body = {\n            name,\n            description,\n            id: queueId || uuid__WEBPACK_IMPORTED_MODULE_10__[\"default\"](),\n        };\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), `${this.apiUrl}/annotation-queues`, {\n            method: \"POST\",\n            headers: { ...this.headers, \"Content-Type\": \"application/json\" },\n            body: JSON.stringify(Object.fromEntries(Object.entries(body).filter(([_, v]) => v !== undefined))),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await (0,_utils_error_js__WEBPACK_IMPORTED_MODULE_7__.raiseForStatus)(response, \"create annotation queue\");\n        const data = await response.json();\n        return data;\n    }\n    /**\n     * Read an annotation queue with the specified queue ID.\n     * @param queueId - The ID of the annotation queue to read\n     * @returns The AnnotationQueue object\n     */\n    async readAnnotationQueue(queueId) {\n        // TODO: Replace when actual endpoint is added\n        const queueIteratorResult = await this.listAnnotationQueues({\n            queueIds: [queueId],\n        }).next();\n        if (queueIteratorResult.done) {\n            throw new Error(`Annotation queue with ID ${queueId} not found`);\n        }\n        return queueIteratorResult.value;\n    }\n    /**\n     * Update an annotation queue with the specified queue ID.\n     * @param queueId - The ID of the annotation queue to update\n     * @param options - The options for updating the annotation queue\n     * @param options.name - The new name for the annotation queue\n     * @param options.description - The new description for the annotation queue\n     */\n    async updateAnnotationQueue(queueId, options) {\n        const { name, description } = options;\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), `${this.apiUrl}/annotation-queues/${(0,_utils_uuid_js__WEBPACK_IMPORTED_MODULE_4__.assertUuid)(queueId, \"queueId\")}`, {\n            method: \"PATCH\",\n            headers: { ...this.headers, \"Content-Type\": \"application/json\" },\n            body: JSON.stringify({ name, description }),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await (0,_utils_error_js__WEBPACK_IMPORTED_MODULE_7__.raiseForStatus)(response, \"update annotation queue\");\n    }\n    /**\n     * Delete an annotation queue with the specified queue ID.\n     * @param queueId - The ID of the annotation queue to delete\n     */\n    async deleteAnnotationQueue(queueId) {\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), `${this.apiUrl}/annotation-queues/${(0,_utils_uuid_js__WEBPACK_IMPORTED_MODULE_4__.assertUuid)(queueId, \"queueId\")}`, {\n            method: \"DELETE\",\n            headers: { ...this.headers, Accept: \"application/json\" },\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await (0,_utils_error_js__WEBPACK_IMPORTED_MODULE_7__.raiseForStatus)(response, \"delete annotation queue\");\n    }\n    /**\n     * Add runs to an annotation queue with the specified queue ID.\n     * @param queueId - The ID of the annotation queue\n     * @param runIds - The IDs of the runs to be added to the annotation queue\n     */\n    async addRunsToAnnotationQueue(queueId, runIds) {\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), `${this.apiUrl}/annotation-queues/${(0,_utils_uuid_js__WEBPACK_IMPORTED_MODULE_4__.assertUuid)(queueId, \"queueId\")}/runs`, {\n            method: \"POST\",\n            headers: { ...this.headers, \"Content-Type\": \"application/json\" },\n            body: JSON.stringify(runIds.map((id, i) => (0,_utils_uuid_js__WEBPACK_IMPORTED_MODULE_4__.assertUuid)(id, `runIds[${i}]`).toString())),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await (0,_utils_error_js__WEBPACK_IMPORTED_MODULE_7__.raiseForStatus)(response, \"add runs to annotation queue\");\n    }\n    /**\n     * Get a run from an annotation queue at the specified index.\n     * @param queueId - The ID of the annotation queue\n     * @param index - The index of the run to retrieve\n     * @returns A Promise that resolves to a RunWithAnnotationQueueInfo object\n     * @throws {Error} If the run is not found at the given index or for other API-related errors\n     */\n    async getRunFromAnnotationQueue(queueId, index) {\n        const baseUrl = `/annotation-queues/${(0,_utils_uuid_js__WEBPACK_IMPORTED_MODULE_4__.assertUuid)(queueId, \"queueId\")}/run`;\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), `${this.apiUrl}${baseUrl}/${index}`, {\n            method: \"GET\",\n            headers: this.headers,\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await (0,_utils_error_js__WEBPACK_IMPORTED_MODULE_7__.raiseForStatus)(response, \"get run from annotation queue\");\n        return await response.json();\n    }\n    async _currentTenantIsOwner(owner) {\n        const settings = await this._getSettings();\n        return owner == \"-\" || settings.tenant_handle === owner;\n    }\n    async _ownerConflictError(action, owner) {\n        const settings = await this._getSettings();\n        return new Error(`Cannot ${action} for another tenant.\\n\n      Current tenant: ${settings.tenant_handle}\\n\n      Requested tenant: ${owner}`);\n    }\n    async _getLatestCommitHash(promptOwnerAndName) {\n        const res = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), `${this.apiUrl}/commits/${promptOwnerAndName}/?limit=${1}&offset=${0}`, {\n            method: \"GET\",\n            headers: this.headers,\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        const json = await res.json();\n        if (!res.ok) {\n            const detail = typeof json.detail === \"string\"\n                ? json.detail\n                : JSON.stringify(json.detail);\n            const error = new Error(`Error ${res.status}: ${res.statusText}\\n${detail}`);\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            error.statusCode = res.status;\n            throw error;\n        }\n        if (json.commits.length === 0) {\n            return undefined;\n        }\n        return json.commits[0].commit_hash;\n    }\n    async _likeOrUnlikePrompt(promptIdentifier, like) {\n        const [owner, promptName, _] = (0,_utils_prompts_js__WEBPACK_IMPORTED_MODULE_6__.parsePromptIdentifier)(promptIdentifier);\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), `${this.apiUrl}/likes/${owner}/${promptName}`, {\n            method: \"POST\",\n            body: JSON.stringify({ like: like }),\n            headers: { ...this.headers, \"Content-Type\": \"application/json\" },\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await (0,_utils_error_js__WEBPACK_IMPORTED_MODULE_7__.raiseForStatus)(response, `${like ? \"like\" : \"unlike\"} prompt`);\n        return await response.json();\n    }\n    async _getPromptUrl(promptIdentifier) {\n        const [owner, promptName, commitHash] = (0,_utils_prompts_js__WEBPACK_IMPORTED_MODULE_6__.parsePromptIdentifier)(promptIdentifier);\n        if (!(await this._currentTenantIsOwner(owner))) {\n            if (commitHash !== \"latest\") {\n                return `${this.getHostUrl()}/hub/${owner}/${promptName}/${commitHash.substring(0, 8)}`;\n            }\n            else {\n                return `${this.getHostUrl()}/hub/${owner}/${promptName}`;\n            }\n        }\n        else {\n            const settings = await this._getSettings();\n            if (commitHash !== \"latest\") {\n                return `${this.getHostUrl()}/prompts/${promptName}/${commitHash.substring(0, 8)}?organizationId=${settings.id}`;\n            }\n            else {\n                return `${this.getHostUrl()}/prompts/${promptName}?organizationId=${settings.id}`;\n            }\n        }\n    }\n    async promptExists(promptIdentifier) {\n        const prompt = await this.getPrompt(promptIdentifier);\n        return !!prompt;\n    }\n    async likePrompt(promptIdentifier) {\n        return this._likeOrUnlikePrompt(promptIdentifier, true);\n    }\n    async unlikePrompt(promptIdentifier) {\n        return this._likeOrUnlikePrompt(promptIdentifier, false);\n    }\n    async *listCommits(promptOwnerAndName) {\n        for await (const commits of this._getPaginated(`/commits/${promptOwnerAndName}/`, new URLSearchParams(), (res) => res.commits)) {\n            yield* commits;\n        }\n    }\n    async *listPrompts(options) {\n        const params = new URLSearchParams();\n        params.append(\"sort_field\", options?.sortField ?? \"updated_at\");\n        params.append(\"sort_direction\", \"desc\");\n        params.append(\"is_archived\", (!!options?.isArchived).toString());\n        if (options?.isPublic !== undefined) {\n            params.append(\"is_public\", options.isPublic.toString());\n        }\n        if (options?.query) {\n            params.append(\"query\", options.query);\n        }\n        for await (const prompts of this._getPaginated(\"/repos\", params, (res) => res.repos)) {\n            yield* prompts;\n        }\n    }\n    async getPrompt(promptIdentifier) {\n        const [owner, promptName, _] = (0,_utils_prompts_js__WEBPACK_IMPORTED_MODULE_6__.parsePromptIdentifier)(promptIdentifier);\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), `${this.apiUrl}/repos/${owner}/${promptName}`, {\n            method: \"GET\",\n            headers: this.headers,\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        if (response.status === 404) {\n            return null;\n        }\n        await (0,_utils_error_js__WEBPACK_IMPORTED_MODULE_7__.raiseForStatus)(response, \"get prompt\");\n        const result = await response.json();\n        if (result.repo) {\n            return result.repo;\n        }\n        else {\n            return null;\n        }\n    }\n    async createPrompt(promptIdentifier, options) {\n        const settings = await this._getSettings();\n        if (options?.isPublic && !settings.tenant_handle) {\n            throw new Error(`Cannot create a public prompt without first\\n\n        creating a LangChain Hub handle. \n        You can add a handle by creating a public prompt at:\\n\n        https://smith.langchain.com/prompts`);\n        }\n        const [owner, promptName, _] = (0,_utils_prompts_js__WEBPACK_IMPORTED_MODULE_6__.parsePromptIdentifier)(promptIdentifier);\n        if (!(await this._currentTenantIsOwner(owner))) {\n            throw await this._ownerConflictError(\"create a prompt\", owner);\n        }\n        const data = {\n            repo_handle: promptName,\n            ...(options?.description && { description: options.description }),\n            ...(options?.readme && { readme: options.readme }),\n            ...(options?.tags && { tags: options.tags }),\n            is_public: !!options?.isPublic,\n        };\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), `${this.apiUrl}/repos/`, {\n            method: \"POST\",\n            headers: { ...this.headers, \"Content-Type\": \"application/json\" },\n            body: JSON.stringify(data),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await (0,_utils_error_js__WEBPACK_IMPORTED_MODULE_7__.raiseForStatus)(response, \"create prompt\");\n        const { repo } = await response.json();\n        return repo;\n    }\n    async createCommit(promptIdentifier, object, options) {\n        if (!(await this.promptExists(promptIdentifier))) {\n            throw new Error(\"Prompt does not exist, you must create it first.\");\n        }\n        const [owner, promptName, _] = (0,_utils_prompts_js__WEBPACK_IMPORTED_MODULE_6__.parsePromptIdentifier)(promptIdentifier);\n        const resolvedParentCommitHash = options?.parentCommitHash === \"latest\" || !options?.parentCommitHash\n            ? await this._getLatestCommitHash(`${owner}/${promptName}`)\n            : options?.parentCommitHash;\n        const payload = {\n            manifest: JSON.parse(JSON.stringify(object)),\n            parent_commit: resolvedParentCommitHash,\n        };\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), `${this.apiUrl}/commits/${owner}/${promptName}`, {\n            method: \"POST\",\n            headers: { ...this.headers, \"Content-Type\": \"application/json\" },\n            body: JSON.stringify(payload),\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await (0,_utils_error_js__WEBPACK_IMPORTED_MODULE_7__.raiseForStatus)(response, \"create commit\");\n        const result = await response.json();\n        return this._getPromptUrl(`${owner}/${promptName}${result.commit_hash ? `:${result.commit_hash}` : \"\"}`);\n    }\n    async updatePrompt(promptIdentifier, options) {\n        if (!(await this.promptExists(promptIdentifier))) {\n            throw new Error(\"Prompt does not exist, you must create it first.\");\n        }\n        const [owner, promptName] = (0,_utils_prompts_js__WEBPACK_IMPORTED_MODULE_6__.parsePromptIdentifier)(promptIdentifier);\n        if (!(await this._currentTenantIsOwner(owner))) {\n            throw await this._ownerConflictError(\"update a prompt\", owner);\n        }\n        const payload = {};\n        if (options?.description !== undefined)\n            payload.description = options.description;\n        if (options?.readme !== undefined)\n            payload.readme = options.readme;\n        if (options?.tags !== undefined)\n            payload.tags = options.tags;\n        if (options?.isPublic !== undefined)\n            payload.is_public = options.isPublic;\n        if (options?.isArchived !== undefined)\n            payload.is_archived = options.isArchived;\n        // Check if payload is empty\n        if (Object.keys(payload).length === 0) {\n            throw new Error(\"No valid update options provided\");\n        }\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), `${this.apiUrl}/repos/${owner}/${promptName}`, {\n            method: \"PATCH\",\n            body: JSON.stringify(payload),\n            headers: {\n                ...this.headers,\n                \"Content-Type\": \"application/json\",\n            },\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await (0,_utils_error_js__WEBPACK_IMPORTED_MODULE_7__.raiseForStatus)(response, \"update prompt\");\n        return response.json();\n    }\n    async deletePrompt(promptIdentifier) {\n        if (!(await this.promptExists(promptIdentifier))) {\n            throw new Error(\"Prompt does not exist, you must create it first.\");\n        }\n        const [owner, promptName, _] = (0,_utils_prompts_js__WEBPACK_IMPORTED_MODULE_6__.parsePromptIdentifier)(promptIdentifier);\n        if (!(await this._currentTenantIsOwner(owner))) {\n            throw await this._ownerConflictError(\"delete a prompt\", owner);\n        }\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), `${this.apiUrl}/repos/${owner}/${promptName}`, {\n            method: \"DELETE\",\n            headers: this.headers,\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        return await response.json();\n    }\n    async pullPromptCommit(promptIdentifier, options) {\n        const [owner, promptName, commitHash] = (0,_utils_prompts_js__WEBPACK_IMPORTED_MODULE_6__.parsePromptIdentifier)(promptIdentifier);\n        const serverInfo = await this._getServerInfo();\n        const useOptimization = (0,_utils_prompts_js__WEBPACK_IMPORTED_MODULE_6__.isVersionGreaterOrEqual)(serverInfo.version, \"0.5.23\");\n        let passedCommitHash = commitHash;\n        if (!useOptimization && commitHash === \"latest\") {\n            const latestCommitHash = await this._getLatestCommitHash(`${owner}/${promptName}`);\n            if (!latestCommitHash) {\n                throw new Error(\"No commits found\");\n            }\n            else {\n                passedCommitHash = latestCommitHash;\n            }\n        }\n        const response = await this.caller.call((0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_8__._getFetchImplementation)(), `${this.apiUrl}/commits/${owner}/${promptName}/${passedCommitHash}${options?.includeModel ? \"?include_model=true\" : \"\"}`, {\n            method: \"GET\",\n            headers: this.headers,\n            signal: AbortSignal.timeout(this.timeout_ms),\n            ...this.fetchOptions,\n        });\n        await (0,_utils_error_js__WEBPACK_IMPORTED_MODULE_7__.raiseForStatus)(response, \"pull prompt commit\");\n        const result = await response.json();\n        return {\n            owner,\n            repo: promptName,\n            commit_hash: result.commit_hash,\n            manifest: result.manifest,\n            examples: result.examples,\n        };\n    }\n    /**\n     * This method should not be used directly, use `import { pull } from \"langchain/hub\"` instead.\n     * Using this method directly returns the JSON string of the prompt rather than a LangChain object.\n     * @private\n     */\n    async _pullPrompt(promptIdentifier, options) {\n        const promptObject = await this.pullPromptCommit(promptIdentifier, {\n            includeModel: options?.includeModel,\n        });\n        const prompt = JSON.stringify(promptObject.manifest);\n        return prompt;\n    }\n    async pushPrompt(promptIdentifier, options) {\n        // Create or update prompt metadata\n        if (await this.promptExists(promptIdentifier)) {\n            if (options && Object.keys(options).some((key) => key !== \"object\")) {\n                await this.updatePrompt(promptIdentifier, {\n                    description: options?.description,\n                    readme: options?.readme,\n                    tags: options?.tags,\n                    isPublic: options?.isPublic,\n                });\n            }\n        }\n        else {\n            await this.createPrompt(promptIdentifier, {\n                description: options?.description,\n                readme: options?.readme,\n                tags: options?.tags,\n                isPublic: options?.isPublic,\n            });\n        }\n        if (!options?.object) {\n            return await this._getPromptUrl(promptIdentifier);\n        }\n        // Create a commit with the new manifest\n        const url = await this.createCommit(promptIdentifier, options?.object, {\n            parentCommitHash: options?.parentCommitHash,\n        });\n        return url;\n    }\n    /**\n     * Clone a public dataset to your own langsmith tenant.\n     * This operation is idempotent. If you already have a dataset with the given name,\n     * this function will do nothing.\n  \n     * @param {string} tokenOrUrl The token of the public dataset to clone.\n     * @param {Object} [options] Additional options for cloning the dataset.\n     * @param {string} [options.sourceApiUrl] The URL of the langsmith server where the data is hosted. Defaults to the API URL of your current client.\n     * @param {string} [options.datasetName] The name of the dataset to create in your tenant. Defaults to the name of the public dataset.\n     * @returns {Promise<void>}\n     */\n    async clonePublicDataset(tokenOrUrl, options = {}) {\n        const { sourceApiUrl = this.apiUrl, datasetName } = options;\n        const [parsedApiUrl, tokenUuid] = this.parseTokenOrUrl(tokenOrUrl, sourceApiUrl);\n        const sourceClient = new Client({\n            apiUrl: parsedApiUrl,\n            // Placeholder API key not needed anymore in most cases, but\n            // some private deployments may have API key-based rate limiting\n            // that would cause this to fail if we provide no value.\n            apiKey: \"placeholder\",\n        });\n        const ds = await sourceClient.readSharedDataset(tokenUuid);\n        const finalDatasetName = datasetName || ds.name;\n        try {\n            if (await this.hasDataset({ datasetId: finalDatasetName })) {\n                console.log(`Dataset ${finalDatasetName} already exists in your tenant. Skipping.`);\n                return;\n            }\n        }\n        catch (_) {\n            // `.hasDataset` will throw an error if the dataset does not exist.\n            // no-op in that case\n        }\n        // Fetch examples first, then create the dataset\n        const examples = await sourceClient.listSharedExamples(tokenUuid);\n        const dataset = await this.createDataset(finalDatasetName, {\n            description: ds.description,\n            dataType: ds.data_type || \"kv\",\n            inputsSchema: ds.inputs_schema_definition ?? undefined,\n            outputsSchema: ds.outputs_schema_definition ?? undefined,\n        });\n        try {\n            await this.createExamples({\n                inputs: examples.map((e) => e.inputs),\n                outputs: examples.flatMap((e) => (e.outputs ? [e.outputs] : [])),\n                datasetId: dataset.id,\n            });\n        }\n        catch (e) {\n            console.error(`An error occurred while creating dataset ${finalDatasetName}. ` +\n                \"You should delete it manually.\");\n            throw e;\n        }\n    }\n    parseTokenOrUrl(urlOrToken, apiUrl, numParts = 2, kind = \"dataset\") {\n        // Try parsing as UUID\n        try {\n            (0,_utils_uuid_js__WEBPACK_IMPORTED_MODULE_4__.assertUuid)(urlOrToken); // Will throw if it's not a UUID.\n            return [apiUrl, urlOrToken];\n        }\n        catch (_) {\n            // no-op if it's not a uuid\n        }\n        // Parse as URL\n        try {\n            const parsedUrl = new URL(urlOrToken);\n            const pathParts = parsedUrl.pathname\n                .split(\"/\")\n                .filter((part) => part !== \"\");\n            if (pathParts.length >= numParts) {\n                const tokenUuid = pathParts[pathParts.length - numParts];\n                return [apiUrl, tokenUuid];\n            }\n            else {\n                throw new Error(`Invalid public ${kind} URL: ${urlOrToken}`);\n            }\n        }\n        catch (error) {\n            throw new Error(`Invalid public ${kind} URL or token: ${urlOrToken}`);\n        }\n    }\n    /**\n     * Awaits all pending trace batches. Useful for environments where\n     * you need to be sure that all tracing requests finish before execution ends,\n     * such as serverless environments.\n     *\n     * @example\n     * ```\n     * import { Client } from \"langsmith\";\n     *\n     * const client = new Client();\n     *\n     * try {\n     *   // Tracing happens here\n     *   ...\n     * } finally {\n     *   await client.awaitPendingTraceBatches();\n     * }\n     * ```\n     *\n     * @returns A promise that resolves once all currently pending traces have sent.\n     */\n    awaitPendingTraceBatches() {\n        return Promise.all(this.autoBatchQueue.items.map(({ itemPromise }) => itemPromise));\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/langsmith/dist/client.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/langsmith/dist/env.js":
/*!********************************************!*\
  !*** ./node_modules/langsmith/dist/env.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isTracingEnabled: () => (/* binding */ isTracingEnabled)\n/* harmony export */ });\n/* harmony import */ var _utils_env_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/env.js */ \"(rsc)/./node_modules/langsmith/dist/utils/env.js\");\n\nconst isTracingEnabled = (tracingEnabled) => {\n    if (tracingEnabled !== undefined) {\n        return tracingEnabled;\n    }\n    const envVars = [\"TRACING_V2\", \"TRACING\"];\n    return !!envVars.find((envVar) => (0,_utils_env_js__WEBPACK_IMPORTED_MODULE_0__.getLangSmithEnvironmentVariable)(envVar) === \"true\");\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbGFuZ3NtaXRoL2Rpc3QvZW52LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWlFO0FBQzFEO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQ0FBc0MsOEVBQStCO0FBQ3JFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktcGFwZXItcmVhZGluZy1hc3Npc3RhbnQvLi9ub2RlX21vZHVsZXMvbGFuZ3NtaXRoL2Rpc3QvZW52LmpzPzAyNTEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZ2V0TGFuZ1NtaXRoRW52aXJvbm1lbnRWYXJpYWJsZSB9IGZyb20gXCIuL3V0aWxzL2Vudi5qc1wiO1xuZXhwb3J0IGNvbnN0IGlzVHJhY2luZ0VuYWJsZWQgPSAodHJhY2luZ0VuYWJsZWQpID0+IHtcbiAgICBpZiAodHJhY2luZ0VuYWJsZWQgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICByZXR1cm4gdHJhY2luZ0VuYWJsZWQ7XG4gICAgfVxuICAgIGNvbnN0IGVudlZhcnMgPSBbXCJUUkFDSU5HX1YyXCIsIFwiVFJBQ0lOR1wiXTtcbiAgICByZXR1cm4gISFlbnZWYXJzLmZpbmQoKGVudlZhcikgPT4gZ2V0TGFuZ1NtaXRoRW52aXJvbm1lbnRWYXJpYWJsZShlbnZWYXIpID09PSBcInRydWVcIik7XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/langsmith/dist/env.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/langsmith/dist/index.js":
/*!**********************************************!*\
  !*** ./node_modules/langsmith/dist/index.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Client: () => (/* reexport safe */ _client_js__WEBPACK_IMPORTED_MODULE_0__.Client),\n/* harmony export */   RunTree: () => (/* reexport safe */ _run_trees_js__WEBPACK_IMPORTED_MODULE_1__.RunTree),\n/* harmony export */   __version__: () => (/* binding */ __version__),\n/* harmony export */   overrideFetchImplementation: () => (/* reexport safe */ _singletons_fetch_js__WEBPACK_IMPORTED_MODULE_2__.overrideFetchImplementation)\n/* harmony export */ });\n/* harmony import */ var _client_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client.js */ \"(rsc)/./node_modules/langsmith/dist/client.js\");\n/* harmony import */ var _run_trees_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./run_trees.js */ \"(rsc)/./node_modules/langsmith/dist/run_trees.js\");\n/* harmony import */ var _singletons_fetch_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./singletons/fetch.js */ \"(rsc)/./node_modules/langsmith/dist/singletons/fetch.js\");\n\n\n\n// Update using yarn bump-version\nconst __version__ = \"0.1.68\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbGFuZ3NtaXRoL2Rpc3QvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUFxQztBQUNJO0FBQzJCO0FBQ3BFO0FBQ08iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1wYXBlci1yZWFkaW5nLWFzc2lzdGFudC8uL25vZGVfbW9kdWxlcy9sYW5nc21pdGgvZGlzdC9pbmRleC5qcz9iZDYyIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7IENsaWVudCB9IGZyb20gXCIuL2NsaWVudC5qc1wiO1xuZXhwb3J0IHsgUnVuVHJlZSB9IGZyb20gXCIuL3J1bl90cmVlcy5qc1wiO1xuZXhwb3J0IHsgb3ZlcnJpZGVGZXRjaEltcGxlbWVudGF0aW9uIH0gZnJvbSBcIi4vc2luZ2xldG9ucy9mZXRjaC5qc1wiO1xuLy8gVXBkYXRlIHVzaW5nIHlhcm4gYnVtcC12ZXJzaW9uXG5leHBvcnQgY29uc3QgX192ZXJzaW9uX18gPSBcIjAuMS42OFwiO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/langsmith/dist/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/langsmith/dist/run_trees.js":
/*!**************************************************!*\
  !*** ./node_modules/langsmith/dist/run_trees.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RunTree: () => (/* binding */ RunTree),\n/* harmony export */   convertToDottedOrderFormat: () => (/* binding */ convertToDottedOrderFormat),\n/* harmony export */   isRunTree: () => (/* binding */ isRunTree),\n/* harmony export */   isRunnableConfigLike: () => (/* binding */ isRunnableConfigLike)\n/* harmony export */ });\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/langsmith/node_modules/uuid/dist/esm-node/v4.js\");\n/* harmony import */ var _utils_env_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/env.js */ \"(rsc)/./node_modules/langsmith/dist/utils/env.js\");\n/* harmony import */ var _client_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./client.js */ \"(rsc)/./node_modules/langsmith/dist/client.js\");\n/* harmony import */ var _env_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./env.js */ \"(rsc)/./node_modules/langsmith/dist/env.js\");\n/* harmony import */ var _utils_warn_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils/warn.js */ \"(rsc)/./node_modules/langsmith/dist/utils/warn.js\");\n/* harmony import */ var _singletons_constants_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./singletons/constants.js */ \"(rsc)/./node_modules/langsmith/dist/singletons/constants.js\");\n\n\n\n\n\n\nfunction stripNonAlphanumeric(input) {\n    return input.replace(/[-:.]/g, \"\");\n}\nfunction convertToDottedOrderFormat(epoch, runId, executionOrder = 1) {\n    // Date only has millisecond precision, so we use the microseconds to break\n    // possible ties, avoiding incorrect run order\n    const paddedOrder = executionOrder.toFixed(0).slice(0, 3).padStart(3, \"0\");\n    return (stripNonAlphanumeric(`${new Date(epoch).toISOString().slice(0, -1)}${paddedOrder}Z`) + runId);\n}\n/**\n * Baggage header information\n */\nclass Baggage {\n    constructor(metadata, tags) {\n        Object.defineProperty(this, \"metadata\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"tags\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.metadata = metadata;\n        this.tags = tags;\n    }\n    static fromHeader(value) {\n        const items = value.split(\",\");\n        let metadata = {};\n        let tags = [];\n        for (const item of items) {\n            const [key, uriValue] = item.split(\"=\");\n            const value = decodeURIComponent(uriValue);\n            if (key === \"langsmith-metadata\") {\n                metadata = JSON.parse(value);\n            }\n            else if (key === \"langsmith-tags\") {\n                tags = value.split(\",\");\n            }\n        }\n        return new Baggage(metadata, tags);\n    }\n    toHeader() {\n        const items = [];\n        if (this.metadata && Object.keys(this.metadata).length > 0) {\n            items.push(`langsmith-metadata=${encodeURIComponent(JSON.stringify(this.metadata))}`);\n        }\n        if (this.tags && this.tags.length > 0) {\n            items.push(`langsmith-tags=${encodeURIComponent(this.tags.join(\",\"))}`);\n        }\n        return items.join(\",\");\n    }\n}\nclass RunTree {\n    constructor(originalConfig) {\n        Object.defineProperty(this, \"id\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"run_type\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"project_name\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"parent_run\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"child_runs\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"start_time\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"end_time\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"extra\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"tags\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"error\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"serialized\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"inputs\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"outputs\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"reference_example_id\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"client\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"events\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"trace_id\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"dotted_order\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"tracingEnabled\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"execution_order\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"child_execution_order\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        // If you pass in a run tree directly, return a shallow clone\n        if (isRunTree(originalConfig)) {\n            Object.assign(this, { ...originalConfig });\n            return;\n        }\n        const defaultConfig = RunTree.getDefaultConfig();\n        const { metadata, ...config } = originalConfig;\n        const client = config.client ?? RunTree.getSharedClient();\n        const dedupedMetadata = {\n            ...metadata,\n            ...config?.extra?.metadata,\n        };\n        config.extra = { ...config.extra, metadata: dedupedMetadata };\n        Object.assign(this, { ...defaultConfig, ...config, client });\n        if (!this.trace_id) {\n            if (this.parent_run) {\n                this.trace_id = this.parent_run.trace_id ?? this.id;\n            }\n            else {\n                this.trace_id = this.id;\n            }\n        }\n        this.execution_order ??= 1;\n        this.child_execution_order ??= 1;\n        if (!this.dotted_order) {\n            const currentDottedOrder = convertToDottedOrderFormat(this.start_time, this.id, this.execution_order);\n            if (this.parent_run) {\n                this.dotted_order =\n                    this.parent_run.dotted_order + \".\" + currentDottedOrder;\n            }\n            else {\n                this.dotted_order = currentDottedOrder;\n            }\n        }\n    }\n    static getDefaultConfig() {\n        return {\n            id: uuid__WEBPACK_IMPORTED_MODULE_5__[\"default\"](),\n            run_type: \"chain\",\n            project_name: (0,_utils_env_js__WEBPACK_IMPORTED_MODULE_0__.getEnvironmentVariable)(\"LANGCHAIN_PROJECT\") ??\n                (0,_utils_env_js__WEBPACK_IMPORTED_MODULE_0__.getEnvironmentVariable)(\"LANGCHAIN_SESSION\") ?? // TODO: Deprecate\n                \"default\",\n            child_runs: [],\n            api_url: (0,_utils_env_js__WEBPACK_IMPORTED_MODULE_0__.getEnvironmentVariable)(\"LANGCHAIN_ENDPOINT\") ?? \"http://localhost:1984\",\n            api_key: (0,_utils_env_js__WEBPACK_IMPORTED_MODULE_0__.getEnvironmentVariable)(\"LANGCHAIN_API_KEY\"),\n            caller_options: {},\n            start_time: Date.now(),\n            serialized: {},\n            inputs: {},\n            extra: {},\n        };\n    }\n    static getSharedClient() {\n        if (!RunTree.sharedClient) {\n            RunTree.sharedClient = new _client_js__WEBPACK_IMPORTED_MODULE_1__.Client();\n        }\n        return RunTree.sharedClient;\n    }\n    createChild(config) {\n        const child_execution_order = this.child_execution_order + 1;\n        const child = new RunTree({\n            ...config,\n            parent_run: this,\n            project_name: this.project_name,\n            client: this.client,\n            tracingEnabled: this.tracingEnabled,\n            execution_order: child_execution_order,\n            child_execution_order: child_execution_order,\n        });\n        // Copy context vars over into the new run tree.\n        if (_singletons_constants_js__WEBPACK_IMPORTED_MODULE_4__._LC_CONTEXT_VARIABLES_KEY in this) {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            child[_singletons_constants_js__WEBPACK_IMPORTED_MODULE_4__._LC_CONTEXT_VARIABLES_KEY] =\n                this[_singletons_constants_js__WEBPACK_IMPORTED_MODULE_4__._LC_CONTEXT_VARIABLES_KEY];\n        }\n        const LC_CHILD = Symbol.for(\"lc:child_config\");\n        const presentConfig = config.extra?.[LC_CHILD] ??\n            this.extra[LC_CHILD];\n        // tracing for LangChain is defined by the _parentRunId and runMap of the tracer\n        if (isRunnableConfigLike(presentConfig)) {\n            const newConfig = { ...presentConfig };\n            const callbacks = isCallbackManagerLike(newConfig.callbacks)\n                ? newConfig.callbacks.copy?.()\n                : undefined;\n            if (callbacks) {\n                // update the parent run id\n                Object.assign(callbacks, { _parentRunId: child.id });\n                // only populate if we're in a newer LC.JS version\n                callbacks.handlers\n                    ?.find(isLangChainTracerLike)\n                    ?.updateFromRunTree?.(child);\n                newConfig.callbacks = callbacks;\n            }\n            child.extra[LC_CHILD] = newConfig;\n        }\n        // propagate child_execution_order upwards\n        const visited = new Set();\n        let current = this;\n        while (current != null && !visited.has(current.id)) {\n            visited.add(current.id);\n            current.child_execution_order = Math.max(current.child_execution_order, child_execution_order);\n            current = current.parent_run;\n        }\n        this.child_runs.push(child);\n        return child;\n    }\n    async end(outputs, error, endTime = Date.now(), metadata) {\n        this.outputs = this.outputs ?? outputs;\n        this.error = this.error ?? error;\n        this.end_time = this.end_time ?? endTime;\n        if (metadata && Object.keys(metadata).length > 0) {\n            this.extra = this.extra\n                ? { ...this.extra, metadata: { ...this.extra.metadata, ...metadata } }\n                : { metadata };\n        }\n    }\n    _convertToCreate(run, runtimeEnv, excludeChildRuns = true) {\n        const runExtra = run.extra ?? {};\n        if (!runExtra.runtime) {\n            runExtra.runtime = {};\n        }\n        if (runtimeEnv) {\n            for (const [k, v] of Object.entries(runtimeEnv)) {\n                if (!runExtra.runtime[k]) {\n                    runExtra.runtime[k] = v;\n                }\n            }\n        }\n        let child_runs;\n        let parent_run_id;\n        if (!excludeChildRuns) {\n            child_runs = run.child_runs.map((child_run) => this._convertToCreate(child_run, runtimeEnv, excludeChildRuns));\n            parent_run_id = undefined;\n        }\n        else {\n            parent_run_id = run.parent_run?.id;\n            child_runs = [];\n        }\n        const persistedRun = {\n            id: run.id,\n            name: run.name,\n            start_time: run.start_time,\n            end_time: run.end_time,\n            run_type: run.run_type,\n            reference_example_id: run.reference_example_id,\n            extra: runExtra,\n            serialized: run.serialized,\n            error: run.error,\n            inputs: run.inputs,\n            outputs: run.outputs,\n            session_name: run.project_name,\n            child_runs: child_runs,\n            parent_run_id: parent_run_id,\n            trace_id: run.trace_id,\n            dotted_order: run.dotted_order,\n            tags: run.tags,\n        };\n        return persistedRun;\n    }\n    async postRun(excludeChildRuns = true) {\n        try {\n            const runtimeEnv = (0,_utils_env_js__WEBPACK_IMPORTED_MODULE_0__.getRuntimeEnvironment)();\n            const runCreate = await this._convertToCreate(this, runtimeEnv, true);\n            await this.client.createRun(runCreate);\n            if (!excludeChildRuns) {\n                (0,_utils_warn_js__WEBPACK_IMPORTED_MODULE_3__.warnOnce)(\"Posting with excludeChildRuns=false is deprecated and will be removed in a future version.\");\n                for (const childRun of this.child_runs) {\n                    await childRun.postRun(false);\n                }\n            }\n        }\n        catch (error) {\n            console.error(`Error in postRun for run ${this.id}:`, error);\n        }\n    }\n    async patchRun() {\n        try {\n            const runUpdate = {\n                end_time: this.end_time,\n                error: this.error,\n                inputs: this.inputs,\n                outputs: this.outputs,\n                parent_run_id: this.parent_run?.id,\n                reference_example_id: this.reference_example_id,\n                extra: this.extra,\n                events: this.events,\n                dotted_order: this.dotted_order,\n                trace_id: this.trace_id,\n                tags: this.tags,\n            };\n            await this.client.updateRun(this.id, runUpdate);\n        }\n        catch (error) {\n            console.error(`Error in patchRun for run ${this.id}`, error);\n        }\n    }\n    toJSON() {\n        return this._convertToCreate(this, undefined, false);\n    }\n    static fromRunnableConfig(parentConfig, props) {\n        // We only handle the callback manager case for now\n        const callbackManager = parentConfig?.callbacks;\n        let parentRun;\n        let projectName;\n        let client;\n        let tracingEnabled = (0,_env_js__WEBPACK_IMPORTED_MODULE_2__.isTracingEnabled)();\n        if (callbackManager) {\n            const parentRunId = callbackManager?.getParentRunId?.() ?? \"\";\n            const langChainTracer = callbackManager?.handlers?.find((handler) => handler?.name == \"langchain_tracer\");\n            parentRun = langChainTracer?.getRun?.(parentRunId);\n            projectName = langChainTracer?.projectName;\n            client = langChainTracer?.client;\n            tracingEnabled = tracingEnabled || !!langChainTracer;\n        }\n        if (!parentRun) {\n            return new RunTree({\n                ...props,\n                client,\n                tracingEnabled,\n                project_name: projectName,\n            });\n        }\n        const parentRunTree = new RunTree({\n            name: parentRun.name,\n            id: parentRun.id,\n            trace_id: parentRun.trace_id,\n            dotted_order: parentRun.dotted_order,\n            client,\n            tracingEnabled,\n            project_name: projectName,\n            tags: [\n                ...new Set((parentRun?.tags ?? []).concat(parentConfig?.tags ?? [])),\n            ],\n            extra: {\n                metadata: {\n                    ...parentRun?.extra?.metadata,\n                    ...parentConfig?.metadata,\n                },\n            },\n        });\n        return parentRunTree.createChild(props);\n    }\n    static fromDottedOrder(dottedOrder) {\n        return this.fromHeaders({ \"langsmith-trace\": dottedOrder });\n    }\n    static fromHeaders(headers, inheritArgs) {\n        const rawHeaders = \"get\" in headers && typeof headers.get === \"function\"\n            ? {\n                \"langsmith-trace\": headers.get(\"langsmith-trace\"),\n                baggage: headers.get(\"baggage\"),\n            }\n            : headers;\n        const headerTrace = rawHeaders[\"langsmith-trace\"];\n        if (!headerTrace || typeof headerTrace !== \"string\")\n            return undefined;\n        const parentDottedOrder = headerTrace.trim();\n        const parsedDottedOrder = parentDottedOrder.split(\".\").map((part) => {\n            const [strTime, uuid] = part.split(\"Z\");\n            return { strTime, time: Date.parse(strTime + \"Z\"), uuid };\n        });\n        const traceId = parsedDottedOrder[0].uuid;\n        const config = {\n            ...inheritArgs,\n            name: inheritArgs?.[\"name\"] ?? \"parent\",\n            run_type: inheritArgs?.[\"run_type\"] ?? \"chain\",\n            start_time: inheritArgs?.[\"start_time\"] ?? Date.now(),\n            id: parsedDottedOrder.at(-1)?.uuid,\n            trace_id: traceId,\n            dotted_order: parentDottedOrder,\n        };\n        if (rawHeaders[\"baggage\"] && typeof rawHeaders[\"baggage\"] === \"string\") {\n            const baggage = Baggage.fromHeader(rawHeaders[\"baggage\"]);\n            config.metadata = baggage.metadata;\n            config.tags = baggage.tags;\n        }\n        return new RunTree(config);\n    }\n    toHeaders(headers) {\n        const result = {\n            \"langsmith-trace\": this.dotted_order,\n            baggage: new Baggage(this.extra?.metadata, this.tags).toHeader(),\n        };\n        if (headers) {\n            for (const [key, value] of Object.entries(result)) {\n                headers.set(key, value);\n            }\n        }\n        return result;\n    }\n}\nObject.defineProperty(RunTree, \"sharedClient\", {\n    enumerable: true,\n    configurable: true,\n    writable: true,\n    value: null\n});\nfunction isRunTree(x) {\n    return (x !== undefined &&\n        typeof x.createChild === \"function\" &&\n        typeof x.postRun === \"function\");\n}\nfunction isLangChainTracerLike(x) {\n    return (typeof x === \"object\" &&\n        x != null &&\n        typeof x.name === \"string\" &&\n        x.name === \"langchain_tracer\");\n}\nfunction containsLangChainTracerLike(x) {\n    return (Array.isArray(x) && x.some((callback) => isLangChainTracerLike(callback)));\n}\nfunction isCallbackManagerLike(x) {\n    return (typeof x === \"object\" &&\n        x != null &&\n        Array.isArray(x.handlers));\n}\nfunction isRunnableConfigLike(x) {\n    // Check that it's an object with a callbacks arg\n    // that has either a CallbackManagerLike object with a langchain tracer within it\n    // or an array with a LangChainTracerLike object within it\n    return (x !== undefined &&\n        typeof x.callbacks === \"object\" &&\n        // Callback manager with a langchain tracer\n        (containsLangChainTracerLike(x.callbacks?.handlers) ||\n            // Or it's an array with a LangChainTracerLike object within it\n            containsLangChainTracerLike(x.callbacks)));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/langsmith/dist/run_trees.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/langsmith/dist/singletons/constants.js":
/*!*************************************************************!*\
  !*** ./node_modules/langsmith/dist/singletons/constants.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _LC_CONTEXT_VARIABLES_KEY: () => (/* binding */ _LC_CONTEXT_VARIABLES_KEY)\n/* harmony export */ });\nconst _LC_CONTEXT_VARIABLES_KEY = Symbol.for(\"lc:context_variables\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbGFuZ3NtaXRoL2Rpc3Qvc2luZ2xldG9ucy9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktcGFwZXItcmVhZGluZy1hc3Npc3RhbnQvLi9ub2RlX21vZHVsZXMvbGFuZ3NtaXRoL2Rpc3Qvc2luZ2xldG9ucy9jb25zdGFudHMuanM/NDY4MyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgX0xDX0NPTlRFWFRfVkFSSUFCTEVTX0tFWSA9IFN5bWJvbC5mb3IoXCJsYzpjb250ZXh0X3ZhcmlhYmxlc1wiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/langsmith/dist/singletons/constants.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/langsmith/dist/singletons/fetch.js":
/*!*********************************************************!*\
  !*** ./node_modules/langsmith/dist/singletons/fetch.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _getFetchImplementation: () => (/* binding */ _getFetchImplementation),\n/* harmony export */   overrideFetchImplementation: () => (/* binding */ overrideFetchImplementation)\n/* harmony export */ });\n// Wrap the default fetch call due to issues with illegal invocations\n// in some environments:\n// https://stackoverflow.com/questions/69876859/why-does-bind-fix-failed-to-execute-fetch-on-window-illegal-invocation-err\n// @ts-expect-error Broad typing to support a range of fetch implementations\nconst DEFAULT_FETCH_IMPLEMENTATION = (...args) => fetch(...args);\nconst LANGSMITH_FETCH_IMPLEMENTATION_KEY = Symbol.for(\"ls:fetch_implementation\");\n/**\n * Overrides the fetch implementation used for LangSmith calls.\n * You should use this if you need to use an implementation of fetch\n * other than the default global (e.g. for dealing with proxies).\n * @param fetch The new fetch functino to use.\n */\nconst overrideFetchImplementation = (fetch) => {\n    globalThis[LANGSMITH_FETCH_IMPLEMENTATION_KEY] = fetch;\n};\n/**\n * @internal\n */\nconst _getFetchImplementation = () => {\n    return (globalThis[LANGSMITH_FETCH_IMPLEMENTATION_KEY] ??\n        DEFAULT_FETCH_IMPLEMENTATION);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbGFuZ3NtaXRoL2Rpc3Qvc2luZ2xldG9ucy9mZXRjaC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktcGFwZXItcmVhZGluZy1hc3Npc3RhbnQvLi9ub2RlX21vZHVsZXMvbGFuZ3NtaXRoL2Rpc3Qvc2luZ2xldG9ucy9mZXRjaC5qcz83YmI0Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIFdyYXAgdGhlIGRlZmF1bHQgZmV0Y2ggY2FsbCBkdWUgdG8gaXNzdWVzIHdpdGggaWxsZWdhbCBpbnZvY2F0aW9uc1xuLy8gaW4gc29tZSBlbnZpcm9ubWVudHM6XG4vLyBodHRwczovL3N0YWNrb3ZlcmZsb3cuY29tL3F1ZXN0aW9ucy82OTg3Njg1OS93aHktZG9lcy1iaW5kLWZpeC1mYWlsZWQtdG8tZXhlY3V0ZS1mZXRjaC1vbi13aW5kb3ctaWxsZWdhbC1pbnZvY2F0aW9uLWVyclxuLy8gQHRzLWV4cGVjdC1lcnJvciBCcm9hZCB0eXBpbmcgdG8gc3VwcG9ydCBhIHJhbmdlIG9mIGZldGNoIGltcGxlbWVudGF0aW9uc1xuY29uc3QgREVGQVVMVF9GRVRDSF9JTVBMRU1FTlRBVElPTiA9ICguLi5hcmdzKSA9PiBmZXRjaCguLi5hcmdzKTtcbmNvbnN0IExBTkdTTUlUSF9GRVRDSF9JTVBMRU1FTlRBVElPTl9LRVkgPSBTeW1ib2wuZm9yKFwibHM6ZmV0Y2hfaW1wbGVtZW50YXRpb25cIik7XG4vKipcbiAqIE92ZXJyaWRlcyB0aGUgZmV0Y2ggaW1wbGVtZW50YXRpb24gdXNlZCBmb3IgTGFuZ1NtaXRoIGNhbGxzLlxuICogWW91IHNob3VsZCB1c2UgdGhpcyBpZiB5b3UgbmVlZCB0byB1c2UgYW4gaW1wbGVtZW50YXRpb24gb2YgZmV0Y2hcbiAqIG90aGVyIHRoYW4gdGhlIGRlZmF1bHQgZ2xvYmFsIChlLmcuIGZvciBkZWFsaW5nIHdpdGggcHJveGllcykuXG4gKiBAcGFyYW0gZmV0Y2ggVGhlIG5ldyBmZXRjaCBmdW5jdGlubyB0byB1c2UuXG4gKi9cbmV4cG9ydCBjb25zdCBvdmVycmlkZUZldGNoSW1wbGVtZW50YXRpb24gPSAoZmV0Y2gpID0+IHtcbiAgICBnbG9iYWxUaGlzW0xBTkdTTUlUSF9GRVRDSF9JTVBMRU1FTlRBVElPTl9LRVldID0gZmV0Y2g7XG59O1xuLyoqXG4gKiBAaW50ZXJuYWxcbiAqL1xuZXhwb3J0IGNvbnN0IF9nZXRGZXRjaEltcGxlbWVudGF0aW9uID0gKCkgPT4ge1xuICAgIHJldHVybiAoZ2xvYmFsVGhpc1tMQU5HU01JVEhfRkVUQ0hfSU1QTEVNRU5UQVRJT05fS0VZXSA/P1xuICAgICAgICBERUZBVUxUX0ZFVENIX0lNUExFTUVOVEFUSU9OKTtcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/langsmith/dist/singletons/fetch.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/langsmith/dist/utils/_uuid.js":
/*!****************************************************!*\
  !*** ./node_modules/langsmith/dist/utils/_uuid.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assertUuid: () => (/* binding */ assertUuid)\n/* harmony export */ });\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/langsmith/node_modules/uuid/dist/esm-node/validate.js\");\n\nfunction assertUuid(str, which) {\n    if (!uuid__WEBPACK_IMPORTED_MODULE_0__[\"default\"](str)) {\n        const msg = which !== undefined\n            ? `Invalid UUID for ${which}: ${str}`\n            : `Invalid UUID: ${str}`;\n        throw new Error(msg);\n    }\n    return str;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbGFuZ3NtaXRoL2Rpc3QvdXRpbHMvX3V1aWQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBNkI7QUFDdEI7QUFDUCxTQUFTLDRDQUFhO0FBQ3RCO0FBQ0Esa0NBQWtDLE1BQU0sSUFBSSxJQUFJO0FBQ2hELCtCQUErQixJQUFJO0FBQ25DO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktcGFwZXItcmVhZGluZy1hc3Npc3RhbnQvLi9ub2RlX21vZHVsZXMvbGFuZ3NtaXRoL2Rpc3QvdXRpbHMvX3V1aWQuanM/NmMxNiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyB1dWlkIGZyb20gXCJ1dWlkXCI7XG5leHBvcnQgZnVuY3Rpb24gYXNzZXJ0VXVpZChzdHIsIHdoaWNoKSB7XG4gICAgaWYgKCF1dWlkLnZhbGlkYXRlKHN0cikpIHtcbiAgICAgICAgY29uc3QgbXNnID0gd2hpY2ggIT09IHVuZGVmaW5lZFxuICAgICAgICAgICAgPyBgSW52YWxpZCBVVUlEIGZvciAke3doaWNofTogJHtzdHJ9YFxuICAgICAgICAgICAgOiBgSW52YWxpZCBVVUlEOiAke3N0cn1gO1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IobXNnKTtcbiAgICB9XG4gICAgcmV0dXJuIHN0cjtcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/langsmith/dist/utils/_uuid.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/langsmith/dist/utils/async_caller.js":
/*!***********************************************************!*\
  !*** ./node_modules/langsmith/dist/utils/async_caller.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AsyncCaller: () => (/* binding */ AsyncCaller)\n/* harmony export */ });\n/* harmony import */ var p_retry__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! p-retry */ \"(rsc)/./node_modules/p-retry/index.js\");\n/* harmony import */ var p_queue__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! p-queue */ \"(rsc)/./node_modules/p-queue/dist/index.js\");\n/* harmony import */ var _singletons_fetch_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../singletons/fetch.js */ \"(rsc)/./node_modules/langsmith/dist/singletons/fetch.js\");\n\n\n\nconst STATUS_NO_RETRY = [\n    400, // Bad Request\n    401, // Unauthorized\n    403, // Forbidden\n    404, // Not Found\n    405, // Method Not Allowed\n    406, // Not Acceptable\n    407, // Proxy Authentication Required\n    408, // Request Timeout\n];\nconst STATUS_IGNORE = [\n    409, // Conflict\n];\n/**\n * A class that can be used to make async calls with concurrency and retry logic.\n *\n * This is useful for making calls to any kind of \"expensive\" external resource,\n * be it because it's rate-limited, subject to network issues, etc.\n *\n * Concurrent calls are limited by the `maxConcurrency` parameter, which defaults\n * to `Infinity`. This means that by default, all calls will be made in parallel.\n *\n * Retries are limited by the `maxRetries` parameter, which defaults to 6. This\n * means that by default, each call will be retried up to 6 times, with an\n * exponential backoff between each attempt.\n */\nclass AsyncCaller {\n    constructor(params) {\n        Object.defineProperty(this, \"maxConcurrency\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"maxRetries\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"queue\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        Object.defineProperty(this, \"onFailedResponseHook\", {\n            enumerable: true,\n            configurable: true,\n            writable: true,\n            value: void 0\n        });\n        this.maxConcurrency = params.maxConcurrency ?? Infinity;\n        this.maxRetries = params.maxRetries ?? 6;\n        if ( true) {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            this.queue = new p_queue__WEBPACK_IMPORTED_MODULE_1__[\"default\"]({\n                concurrency: this.maxConcurrency,\n            });\n        }\n        else {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            this.queue = new p_queue__WEBPACK_IMPORTED_MODULE_1__({ concurrency: this.maxConcurrency });\n        }\n        this.onFailedResponseHook = params?.onFailedResponseHook;\n    }\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    call(callable, ...args) {\n        const onFailedResponseHook = this.onFailedResponseHook;\n        return this.queue.add(() => p_retry__WEBPACK_IMPORTED_MODULE_0__(() => callable(...args).catch((error) => {\n            // eslint-disable-next-line no-instanceof/no-instanceof\n            if (error instanceof Error) {\n                throw error;\n            }\n            else {\n                throw new Error(error);\n            }\n        }), {\n            async onFailedAttempt(error) {\n                if (error.message.startsWith(\"Cancel\") ||\n                    error.message.startsWith(\"TimeoutError\") ||\n                    error.message.startsWith(\"AbortError\")) {\n                    throw error;\n                }\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                if (error?.code === \"ECONNABORTED\") {\n                    throw error;\n                }\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                const response = error?.response;\n                const status = response?.status;\n                if (status) {\n                    if (STATUS_NO_RETRY.includes(+status)) {\n                        throw error;\n                    }\n                    else if (STATUS_IGNORE.includes(+status)) {\n                        return;\n                    }\n                    if (onFailedResponseHook) {\n                        await onFailedResponseHook(response);\n                    }\n                }\n            },\n            // If needed we can change some of the defaults here,\n            // but they're quite sensible.\n            retries: this.maxRetries,\n            randomize: true,\n        }), { throwOnTimeout: true });\n    }\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    callWithOptions(options, callable, ...args) {\n        // Note this doesn't cancel the underlying request,\n        // when available prefer to use the signal option of the underlying call\n        if (options.signal) {\n            return Promise.race([\n                this.call(callable, ...args),\n                new Promise((_, reject) => {\n                    options.signal?.addEventListener(\"abort\", () => {\n                        reject(new Error(\"AbortError\"));\n                    });\n                }),\n            ]);\n        }\n        return this.call(callable, ...args);\n    }\n    fetch(...args) {\n        return this.call(() => (0,_singletons_fetch_js__WEBPACK_IMPORTED_MODULE_2__._getFetchImplementation)()(...args).then((res) => res.ok ? res : Promise.reject(res)));\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/langsmith/dist/utils/async_caller.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/langsmith/dist/utils/env.js":
/*!**************************************************!*\
  !*** ./node_modules/langsmith/dist/utils/env.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getEnv: () => (/* binding */ getEnv),\n/* harmony export */   getEnvironmentVariable: () => (/* binding */ getEnvironmentVariable),\n/* harmony export */   getEnvironmentVariables: () => (/* binding */ getEnvironmentVariables),\n/* harmony export */   getLangChainEnvVars: () => (/* binding */ getLangChainEnvVars),\n/* harmony export */   getLangChainEnvVarsMetadata: () => (/* binding */ getLangChainEnvVarsMetadata),\n/* harmony export */   getLangSmithEnvironmentVariable: () => (/* binding */ getLangSmithEnvironmentVariable),\n/* harmony export */   getRuntimeEnvironment: () => (/* binding */ getRuntimeEnvironment),\n/* harmony export */   getShas: () => (/* binding */ getShas),\n/* harmony export */   isBrowser: () => (/* binding */ isBrowser),\n/* harmony export */   isDeno: () => (/* binding */ isDeno),\n/* harmony export */   isJsDom: () => (/* binding */ isJsDom),\n/* harmony export */   isNode: () => (/* binding */ isNode),\n/* harmony export */   isWebWorker: () => (/* binding */ isWebWorker),\n/* harmony export */   setEnvironmentVariable: () => (/* binding */ setEnvironmentVariable)\n/* harmony export */ });\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../index.js */ \"(rsc)/./node_modules/langsmith/dist/index.js\");\n// Inlined from https://github.com/flexdinesh/browser-or-node\n\nlet globalEnv;\nconst isBrowser = () => typeof window !== \"undefined\" && typeof window.document !== \"undefined\";\nconst isWebWorker = () => typeof globalThis === \"object\" &&\n    globalThis.constructor &&\n    globalThis.constructor.name === \"DedicatedWorkerGlobalScope\";\nconst isJsDom = () => (typeof window !== \"undefined\" && window.name === \"nodejs\") ||\n    (typeof navigator !== \"undefined\" &&\n        (navigator.userAgent.includes(\"Node.js\") ||\n            navigator.userAgent.includes(\"jsdom\")));\n// Supabase Edge Function provides a `Deno` global object\n// without `version` property\nconst isDeno = () => typeof Deno !== \"undefined\";\n// Mark not-as-node if in Supabase Edge Function\nconst isNode = () => typeof process !== \"undefined\" &&\n    typeof process.versions !== \"undefined\" &&\n    typeof process.versions.node !== \"undefined\" &&\n    !isDeno();\nconst getEnv = () => {\n    if (globalEnv) {\n        return globalEnv;\n    }\n    if (isBrowser()) {\n        globalEnv = \"browser\";\n    }\n    else if (isNode()) {\n        globalEnv = \"node\";\n    }\n    else if (isWebWorker()) {\n        globalEnv = \"webworker\";\n    }\n    else if (isJsDom()) {\n        globalEnv = \"jsdom\";\n    }\n    else if (isDeno()) {\n        globalEnv = \"deno\";\n    }\n    else {\n        globalEnv = \"other\";\n    }\n    return globalEnv;\n};\nlet runtimeEnvironment;\nfunction getRuntimeEnvironment() {\n    if (runtimeEnvironment === undefined) {\n        const env = getEnv();\n        const releaseEnv = getShas();\n        runtimeEnvironment = {\n            library: \"langsmith\",\n            runtime: env,\n            sdk: \"langsmith-js\",\n            sdk_version: _index_js__WEBPACK_IMPORTED_MODULE_0__.__version__,\n            ...releaseEnv,\n        };\n    }\n    return runtimeEnvironment;\n}\n/**\n * Retrieves the LangChain-specific environment variables from the current runtime environment.\n * Sensitive keys (containing the word \"key\", \"token\", or \"secret\") have their values redacted for security.\n *\n * @returns {Record<string, string>}\n *  - A record of LangChain-specific environment variables.\n */\nfunction getLangChainEnvVars() {\n    const allEnvVars = getEnvironmentVariables() || {};\n    const envVars = {};\n    for (const [key, value] of Object.entries(allEnvVars)) {\n        if (key.startsWith(\"LANGCHAIN_\") && typeof value === \"string\") {\n            envVars[key] = value;\n        }\n    }\n    for (const key in envVars) {\n        if ((key.toLowerCase().includes(\"key\") ||\n            key.toLowerCase().includes(\"secret\") ||\n            key.toLowerCase().includes(\"token\")) &&\n            typeof envVars[key] === \"string\") {\n            const value = envVars[key];\n            envVars[key] =\n                value.slice(0, 2) + \"*\".repeat(value.length - 4) + value.slice(-2);\n        }\n    }\n    return envVars;\n}\n/**\n * Retrieves the LangChain-specific metadata from the current runtime environment.\n *\n * @returns {Record<string, string>}\n *  - A record of LangChain-specific metadata environment variables.\n */\nfunction getLangChainEnvVarsMetadata() {\n    const allEnvVars = getEnvironmentVariables() || {};\n    const envVars = {};\n    const excluded = [\n        \"LANGCHAIN_API_KEY\",\n        \"LANGCHAIN_ENDPOINT\",\n        \"LANGCHAIN_TRACING_V2\",\n        \"LANGCHAIN_PROJECT\",\n        \"LANGCHAIN_SESSION\",\n    ];\n    for (const [key, value] of Object.entries(allEnvVars)) {\n        if (key.startsWith(\"LANGCHAIN_\") &&\n            typeof value === \"string\" &&\n            !excluded.includes(key) &&\n            !key.toLowerCase().includes(\"key\") &&\n            !key.toLowerCase().includes(\"secret\") &&\n            !key.toLowerCase().includes(\"token\")) {\n            if (key === \"LANGCHAIN_REVISION_ID\") {\n                envVars[\"revision_id\"] = value;\n            }\n            else {\n                envVars[key] = value;\n            }\n        }\n    }\n    return envVars;\n}\n/**\n * Retrieves the environment variables from the current runtime environment.\n *\n * This function is designed to operate in a variety of JS environments,\n * including Node.js, Deno, browsers, etc.\n *\n * @returns {Record<string, string> | undefined}\n *  - A record of environment variables if available.\n *  - `undefined` if the environment does not support or allows access to environment variables.\n */\nfunction getEnvironmentVariables() {\n    try {\n        // Check for Node.js environment\n        // eslint-disable-next-line no-process-env\n        if (typeof process !== \"undefined\" && process.env) {\n            // eslint-disable-next-line no-process-env\n            return Object.entries(process.env).reduce((acc, [key, value]) => {\n                acc[key] = String(value);\n                return acc;\n            }, {});\n        }\n        // For browsers and other environments, we may not have direct access to env variables\n        // Return undefined or any other fallback as required.\n        return undefined;\n    }\n    catch (e) {\n        // Catch any errors that might occur while trying to access environment variables\n        return undefined;\n    }\n}\nfunction getEnvironmentVariable(name) {\n    // Certain Deno setups will throw an error if you try to access environment variables\n    // https://github.com/hwchase17/langchainjs/issues/1412\n    try {\n        return typeof process !== \"undefined\"\n            ? // eslint-disable-next-line no-process-env\n                process.env?.[name]\n            : undefined;\n    }\n    catch (e) {\n        return undefined;\n    }\n}\nfunction getLangSmithEnvironmentVariable(name) {\n    return (getEnvironmentVariable(`LANGSMITH_${name}`) ||\n        getEnvironmentVariable(`LANGCHAIN_${name}`));\n}\nfunction setEnvironmentVariable(name, value) {\n    if (typeof process !== \"undefined\") {\n        // eslint-disable-next-line no-process-env\n        process.env[name] = value;\n    }\n}\nlet cachedCommitSHAs;\n/**\n * Get the Git commit SHA from common environment variables\n * used by different CI/CD platforms.\n * @returns {string | undefined} The Git commit SHA or undefined if not found.\n */\nfunction getShas() {\n    if (cachedCommitSHAs !== undefined) {\n        return cachedCommitSHAs;\n    }\n    const common_release_envs = [\n        \"VERCEL_GIT_COMMIT_SHA\",\n        \"NEXT_PUBLIC_VERCEL_GIT_COMMIT_SHA\",\n        \"COMMIT_REF\",\n        \"RENDER_GIT_COMMIT\",\n        \"CI_COMMIT_SHA\",\n        \"CIRCLE_SHA1\",\n        \"CF_PAGES_COMMIT_SHA\",\n        \"REACT_APP_GIT_SHA\",\n        \"SOURCE_VERSION\",\n        \"GITHUB_SHA\",\n        \"TRAVIS_COMMIT\",\n        \"GIT_COMMIT\",\n        \"BUILD_VCS_NUMBER\",\n        \"bamboo_planRepository_revision\",\n        \"Build.SourceVersion\",\n        \"BITBUCKET_COMMIT\",\n        \"DRONE_COMMIT_SHA\",\n        \"SEMAPHORE_GIT_SHA\",\n        \"BUILDKITE_COMMIT\",\n    ];\n    const shas = {};\n    for (const env of common_release_envs) {\n        const envVar = getEnvironmentVariable(env);\n        if (envVar !== undefined) {\n            shas[env] = envVar;\n        }\n    }\n    cachedCommitSHAs = shas;\n    return shas;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/langsmith/dist/utils/env.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/langsmith/dist/utils/error.js":
/*!****************************************************!*\
  !*** ./node_modules/langsmith/dist/utils/error.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LangSmithConflictError: () => (/* binding */ LangSmithConflictError),\n/* harmony export */   printErrorStackTrace: () => (/* binding */ printErrorStackTrace),\n/* harmony export */   raiseForStatus: () => (/* binding */ raiseForStatus)\n/* harmony export */ });\nfunction getErrorStackTrace(e) {\n    if (typeof e !== \"object\" || e == null)\n        return undefined;\n    if (!(\"stack\" in e) || typeof e.stack !== \"string\")\n        return undefined;\n    let stack = e.stack;\n    const prevLine = `${e}`;\n    if (stack.startsWith(prevLine)) {\n        stack = stack.slice(prevLine.length);\n    }\n    if (stack.startsWith(\"\\n\")) {\n        stack = stack.slice(1);\n    }\n    return stack;\n}\nfunction printErrorStackTrace(e) {\n    const stack = getErrorStackTrace(e);\n    if (stack == null)\n        return;\n    console.error(stack);\n}\n/**\n * LangSmithConflictError\n *\n * Represents an error that occurs when there's a conflict during an operation,\n * typically corresponding to HTTP 409 status code responses.\n *\n * This error is thrown when an attempt to create or modify a resource conflicts\n * with the current state of the resource on the server. Common scenarios include:\n * - Attempting to create a resource that already exists\n * - Trying to update a resource that has been modified by another process\n * - Violating a uniqueness constraint in the data\n *\n * @extends Error\n *\n * @example\n * try {\n *   await createProject(\"existingProject\");\n * } catch (error) {\n *   if (error instanceof ConflictError) {\n *     console.log(\"A conflict occurred:\", error.message);\n *     // Handle the conflict, e.g., by suggesting a different project name\n *   } else {\n *     // Handle other types of errors\n *   }\n * }\n *\n * @property {string} name - Always set to 'ConflictError' for easy identification\n * @property {string} message - Detailed error message including server response\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/409\n */\nclass LangSmithConflictError extends Error {\n    constructor(message) {\n        super(message);\n        this.name = \"LangSmithConflictError\";\n    }\n}\n/**\n * Throws an appropriate error based on the response status and body.\n *\n * @param response - The fetch Response object\n * @param context - Additional context to include in the error message (e.g., operation being performed)\n * @throws {LangSmithConflictError} When the response status is 409\n * @throws {Error} For all other non-ok responses\n */\nasync function raiseForStatus(response, context, consume) {\n    // consume the response body to release the connection\n    // https://undici.nodejs.org/#/?id=garbage-collection\n    let errorBody;\n    if (response.ok) {\n        if (consume) {\n            errorBody = await response.text();\n        }\n        return;\n    }\n    errorBody = await response.text();\n    const fullMessage = `Failed to ${context}. Received status [${response.status}]: ${response.statusText}. Server response: ${errorBody}`;\n    if (response.status === 409) {\n        throw new LangSmithConflictError(fullMessage);\n    }\n    throw new Error(fullMessage);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/langsmith/dist/utils/error.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/langsmith/dist/utils/fast-safe-stringify/index.js":
/*!************************************************************************!*\
  !*** ./node_modules/langsmith/dist/utils/fast-safe-stringify/index.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   stringify: () => (/* binding */ stringify)\n/* harmony export */ });\n/* eslint-disable */\n// @ts-nocheck\nvar LIMIT_REPLACE_NODE = \"[...]\";\nvar CIRCULAR_REPLACE_NODE = { result: \"[Circular]\" };\nvar arr = [];\nvar replacerStack = [];\nfunction defaultOptions() {\n    return {\n        depthLimit: Number.MAX_SAFE_INTEGER,\n        edgesLimit: Number.MAX_SAFE_INTEGER,\n    };\n}\n// Regular stringify\nfunction stringify(obj, replacer, spacer, options) {\n    try {\n        return JSON.stringify(obj, replacer, spacer);\n    }\n    catch (e) {\n        // Fall back to more complex stringify if circular reference\n        if (!e.message?.includes(\"Converting circular structure to JSON\")) {\n            console.warn(\"[WARNING]: LangSmith received unserializable value.\");\n            return \"[Unserializable]\";\n        }\n        console.warn(\"[WARNING]: LangSmith received circular JSON. This will decrease tracer performance.\");\n        if (typeof options === \"undefined\") {\n            options = defaultOptions();\n        }\n        decirc(obj, \"\", 0, [], undefined, 0, options);\n        var res;\n        try {\n            if (replacerStack.length === 0) {\n                res = JSON.stringify(obj, replacer, spacer);\n            }\n            else {\n                res = JSON.stringify(obj, replaceGetterValues(replacer), spacer);\n            }\n        }\n        catch (_) {\n            return JSON.stringify(\"[unable to serialize, circular reference is too complex to analyze]\");\n        }\n        finally {\n            while (arr.length !== 0) {\n                var part = arr.pop();\n                if (part.length === 4) {\n                    Object.defineProperty(part[0], part[1], part[3]);\n                }\n                else {\n                    part[0][part[1]] = part[2];\n                }\n            }\n        }\n        return res;\n    }\n}\nfunction setReplace(replace, val, k, parent) {\n    var propertyDescriptor = Object.getOwnPropertyDescriptor(parent, k);\n    if (propertyDescriptor.get !== undefined) {\n        if (propertyDescriptor.configurable) {\n            Object.defineProperty(parent, k, { value: replace });\n            arr.push([parent, k, val, propertyDescriptor]);\n        }\n        else {\n            replacerStack.push([val, k, replace]);\n        }\n    }\n    else {\n        parent[k] = replace;\n        arr.push([parent, k, val]);\n    }\n}\nfunction decirc(val, k, edgeIndex, stack, parent, depth, options) {\n    depth += 1;\n    var i;\n    if (typeof val === \"object\" && val !== null) {\n        for (i = 0; i < stack.length; i++) {\n            if (stack[i] === val) {\n                setReplace(CIRCULAR_REPLACE_NODE, val, k, parent);\n                return;\n            }\n        }\n        if (typeof options.depthLimit !== \"undefined\" &&\n            depth > options.depthLimit) {\n            setReplace(LIMIT_REPLACE_NODE, val, k, parent);\n            return;\n        }\n        if (typeof options.edgesLimit !== \"undefined\" &&\n            edgeIndex + 1 > options.edgesLimit) {\n            setReplace(LIMIT_REPLACE_NODE, val, k, parent);\n            return;\n        }\n        stack.push(val);\n        // Optimize for Arrays. Big arrays could kill the performance otherwise!\n        if (Array.isArray(val)) {\n            for (i = 0; i < val.length; i++) {\n                decirc(val[i], i, i, stack, val, depth, options);\n            }\n        }\n        else {\n            var keys = Object.keys(val);\n            for (i = 0; i < keys.length; i++) {\n                var key = keys[i];\n                decirc(val[key], key, i, stack, val, depth, options);\n            }\n        }\n        stack.pop();\n    }\n}\n// Stable-stringify\nfunction compareFunction(a, b) {\n    if (a < b) {\n        return -1;\n    }\n    if (a > b) {\n        return 1;\n    }\n    return 0;\n}\nfunction deterministicStringify(obj, replacer, spacer, options) {\n    if (typeof options === \"undefined\") {\n        options = defaultOptions();\n    }\n    var tmp = deterministicDecirc(obj, \"\", 0, [], undefined, 0, options) || obj;\n    var res;\n    try {\n        if (replacerStack.length === 0) {\n            res = JSON.stringify(tmp, replacer, spacer);\n        }\n        else {\n            res = JSON.stringify(tmp, replaceGetterValues(replacer), spacer);\n        }\n    }\n    catch (_) {\n        return JSON.stringify(\"[unable to serialize, circular reference is too complex to analyze]\");\n    }\n    finally {\n        // Ensure that we restore the object as it was.\n        while (arr.length !== 0) {\n            var part = arr.pop();\n            if (part.length === 4) {\n                Object.defineProperty(part[0], part[1], part[3]);\n            }\n            else {\n                part[0][part[1]] = part[2];\n            }\n        }\n    }\n    return res;\n}\nfunction deterministicDecirc(val, k, edgeIndex, stack, parent, depth, options) {\n    depth += 1;\n    var i;\n    if (typeof val === \"object\" && val !== null) {\n        for (i = 0; i < stack.length; i++) {\n            if (stack[i] === val) {\n                setReplace(CIRCULAR_REPLACE_NODE, val, k, parent);\n                return;\n            }\n        }\n        try {\n            if (typeof val.toJSON === \"function\") {\n                return;\n            }\n        }\n        catch (_) {\n            return;\n        }\n        if (typeof options.depthLimit !== \"undefined\" &&\n            depth > options.depthLimit) {\n            setReplace(LIMIT_REPLACE_NODE, val, k, parent);\n            return;\n        }\n        if (typeof options.edgesLimit !== \"undefined\" &&\n            edgeIndex + 1 > options.edgesLimit) {\n            setReplace(LIMIT_REPLACE_NODE, val, k, parent);\n            return;\n        }\n        stack.push(val);\n        // Optimize for Arrays. Big arrays could kill the performance otherwise!\n        if (Array.isArray(val)) {\n            for (i = 0; i < val.length; i++) {\n                deterministicDecirc(val[i], i, i, stack, val, depth, options);\n            }\n        }\n        else {\n            // Create a temporary object in the required way\n            var tmp = {};\n            var keys = Object.keys(val).sort(compareFunction);\n            for (i = 0; i < keys.length; i++) {\n                var key = keys[i];\n                deterministicDecirc(val[key], key, i, stack, val, depth, options);\n                tmp[key] = val[key];\n            }\n            if (typeof parent !== \"undefined\") {\n                arr.push([parent, k, val]);\n                parent[k] = tmp;\n            }\n            else {\n                return tmp;\n            }\n        }\n        stack.pop();\n    }\n}\n// wraps replacer function to handle values we couldn't replace\n// and mark them as replaced value\nfunction replaceGetterValues(replacer) {\n    replacer =\n        typeof replacer !== \"undefined\"\n            ? replacer\n            : function (k, v) {\n                return v;\n            };\n    return function (key, val) {\n        if (replacerStack.length > 0) {\n            for (var i = 0; i < replacerStack.length; i++) {\n                var part = replacerStack[i];\n                if (part[1] === key && part[0] === val) {\n                    val = part[2];\n                    replacerStack.splice(i, 1);\n                    break;\n                }\n            }\n        }\n        return replacer.call(this, key, val);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/langsmith/dist/utils/fast-safe-stringify/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/langsmith/dist/utils/messages.js":
/*!*******************************************************!*\
  !*** ./node_modules/langsmith/dist/utils/messages.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   convertLangChainMessageToExample: () => (/* binding */ convertLangChainMessageToExample),\n/* harmony export */   isLangChainMessage: () => (/* binding */ isLangChainMessage)\n/* harmony export */ });\nfunction isLangChainMessage(\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nmessage) {\n    return typeof message?._getType === \"function\";\n}\nfunction convertLangChainMessageToExample(message) {\n    const converted = {\n        type: message._getType(),\n        data: { content: message.content },\n    };\n    // Check for presence of keys in additional_kwargs\n    if (message?.additional_kwargs &&\n        Object.keys(message.additional_kwargs).length > 0) {\n        converted.data.additional_kwargs = { ...message.additional_kwargs };\n    }\n    return converted;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbGFuZ3NtaXRoL2Rpc3QvdXRpbHMvbWVzc2FnZXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0EsZ0JBQWdCLDBCQUEwQjtBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBLDZDQUE2QztBQUM3QztBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1wYXBlci1yZWFkaW5nLWFzc2lzdGFudC8uL25vZGVfbW9kdWxlcy9sYW5nc21pdGgvZGlzdC91dGlscy9tZXNzYWdlcy5qcz83OGFmIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBpc0xhbmdDaGFpbk1lc3NhZ2UoXG4vLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgQHR5cGVzY3JpcHQtZXNsaW50L25vLWV4cGxpY2l0LWFueVxubWVzc2FnZSkge1xuICAgIHJldHVybiB0eXBlb2YgbWVzc2FnZT8uX2dldFR5cGUgPT09IFwiZnVuY3Rpb25cIjtcbn1cbmV4cG9ydCBmdW5jdGlvbiBjb252ZXJ0TGFuZ0NoYWluTWVzc2FnZVRvRXhhbXBsZShtZXNzYWdlKSB7XG4gICAgY29uc3QgY29udmVydGVkID0ge1xuICAgICAgICB0eXBlOiBtZXNzYWdlLl9nZXRUeXBlKCksXG4gICAgICAgIGRhdGE6IHsgY29udGVudDogbWVzc2FnZS5jb250ZW50IH0sXG4gICAgfTtcbiAgICAvLyBDaGVjayBmb3IgcHJlc2VuY2Ugb2Yga2V5cyBpbiBhZGRpdGlvbmFsX2t3YXJnc1xuICAgIGlmIChtZXNzYWdlPy5hZGRpdGlvbmFsX2t3YXJncyAmJlxuICAgICAgICBPYmplY3Qua2V5cyhtZXNzYWdlLmFkZGl0aW9uYWxfa3dhcmdzKS5sZW5ndGggPiAwKSB7XG4gICAgICAgIGNvbnZlcnRlZC5kYXRhLmFkZGl0aW9uYWxfa3dhcmdzID0geyAuLi5tZXNzYWdlLmFkZGl0aW9uYWxfa3dhcmdzIH07XG4gICAgfVxuICAgIHJldHVybiBjb252ZXJ0ZWQ7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/langsmith/dist/utils/messages.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/langsmith/dist/utils/prompts.js":
/*!******************************************************!*\
  !*** ./node_modules/langsmith/dist/utils/prompts.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isVersionGreaterOrEqual: () => (/* binding */ isVersionGreaterOrEqual),\n/* harmony export */   parsePromptIdentifier: () => (/* binding */ parsePromptIdentifier)\n/* harmony export */ });\n/* harmony import */ var semver__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! semver */ \"(rsc)/./node_modules/semver/index.js\");\n\nfunction isVersionGreaterOrEqual(current_version, target_version) {\n    const current = (0,semver__WEBPACK_IMPORTED_MODULE_0__.parse)(current_version);\n    const target = (0,semver__WEBPACK_IMPORTED_MODULE_0__.parse)(target_version);\n    if (!current || !target) {\n        throw new Error(\"Invalid version format.\");\n    }\n    return current.compare(target) >= 0;\n}\nfunction parsePromptIdentifier(identifier) {\n    if (!identifier ||\n        identifier.split(\"/\").length > 2 ||\n        identifier.startsWith(\"/\") ||\n        identifier.endsWith(\"/\") ||\n        identifier.split(\":\").length > 2) {\n        throw new Error(`Invalid identifier format: ${identifier}`);\n    }\n    const [ownerNamePart, commitPart] = identifier.split(\":\");\n    const commit = commitPart || \"latest\";\n    if (ownerNamePart.includes(\"/\")) {\n        const [owner, name] = ownerNamePart.split(\"/\", 2);\n        if (!owner || !name) {\n            throw new Error(`Invalid identifier format: ${identifier}`);\n        }\n        return [owner, name, commit];\n    }\n    else {\n        if (!ownerNamePart) {\n            throw new Error(`Invalid identifier format: ${identifier}`);\n        }\n        return [\"-\", ownerNamePart, commit];\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/langsmith/dist/utils/prompts.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/langsmith/dist/utils/warn.js":
/*!***************************************************!*\
  !*** ./node_modules/langsmith/dist/utils/warn.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   warnOnce: () => (/* binding */ warnOnce)\n/* harmony export */ });\nconst warnedMessages = {};\nfunction warnOnce(message) {\n    if (!warnedMessages[message]) {\n        console.warn(message);\n        warnedMessages[message] = true;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbGFuZ3NtaXRoL2Rpc3QvdXRpbHMvd2Fybi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1wYXBlci1yZWFkaW5nLWFzc2lzdGFudC8uL25vZGVfbW9kdWxlcy9sYW5nc21pdGgvZGlzdC91dGlscy93YXJuLmpzPzU5MzQiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3Qgd2FybmVkTWVzc2FnZXMgPSB7fTtcbmV4cG9ydCBmdW5jdGlvbiB3YXJuT25jZShtZXNzYWdlKSB7XG4gICAgaWYgKCF3YXJuZWRNZXNzYWdlc1ttZXNzYWdlXSkge1xuICAgICAgICBjb25zb2xlLndhcm4obWVzc2FnZSk7XG4gICAgICAgIHdhcm5lZE1lc3NhZ2VzW21lc3NhZ2VdID0gdHJ1ZTtcbiAgICB9XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/langsmith/dist/utils/warn.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/langsmith/index.js":
/*!*****************************************!*\
  !*** ./node_modules/langsmith/index.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Client: () => (/* reexport safe */ _dist_index_js__WEBPACK_IMPORTED_MODULE_0__.Client),
/* harmony export */   RunTree: () => (/* reexport safe */ _dist_index_js__WEBPACK_IMPORTED_MODULE_0__.RunTree),
/* harmony export */   __version__: () => (/* reexport safe */ _dist_index_js__WEBPACK_IMPORTED_MODULE_0__.__version__),
/* harmony export */   overrideFetchImplementation: () => (/* reexport safe */ _dist_index_js__WEBPACK_IMPORTED_MODULE_0__.overrideFetchImplementation)
/* harmony export */ });
/* harmony import */ var _dist_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./dist/index.js */ "(rsc)/./node_modules/langsmith/dist/index.js");


/***/ })

};
;