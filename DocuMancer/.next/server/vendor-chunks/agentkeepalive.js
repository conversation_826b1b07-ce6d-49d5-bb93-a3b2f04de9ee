"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/agentkeepalive";
exports.ids = ["vendor-chunks/agentkeepalive"];
exports.modules = {

/***/ "(rsc)/./node_modules/agentkeepalive/index.js":
/*!**********************************************!*\
  !*** ./node_modules/agentkeepalive/index.js ***!
  \**********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst HttpAgent = __webpack_require__(/*! ./lib/agent */ \"(rsc)/./node_modules/agentkeepalive/lib/agent.js\");\nmodule.exports = HttpAgent;\nmodule.exports.HttpAgent = HttpAgent;\nmodule.exports.HttpsAgent = __webpack_require__(/*! ./lib/https_agent */ \"(rsc)/./node_modules/agentkeepalive/lib/https_agent.js\");\nmodule.exports.constants = __webpack_require__(/*! ./lib/constants */ \"(rsc)/./node_modules/agentkeepalive/lib/constants.js\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvYWdlbnRrZWVwYWxpdmUvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsa0JBQWtCLG1CQUFPLENBQUMscUVBQWE7QUFDdkM7QUFDQSx3QkFBd0I7QUFDeEIsa0lBQXdEO0FBQ3hELDZIQUFxRCIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXBhcGVyLXJlYWRpbmctYXNzaXN0YW50Ly4vbm9kZV9tb2R1bGVzL2FnZW50a2VlcGFsaXZlL2luZGV4LmpzPzc3NzUiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5jb25zdCBIdHRwQWdlbnQgPSByZXF1aXJlKCcuL2xpYi9hZ2VudCcpO1xubW9kdWxlLmV4cG9ydHMgPSBIdHRwQWdlbnQ7XG5tb2R1bGUuZXhwb3J0cy5IdHRwQWdlbnQgPSBIdHRwQWdlbnQ7XG5tb2R1bGUuZXhwb3J0cy5IdHRwc0FnZW50ID0gcmVxdWlyZSgnLi9saWIvaHR0cHNfYWdlbnQnKTtcbm1vZHVsZS5leHBvcnRzLmNvbnN0YW50cyA9IHJlcXVpcmUoJy4vbGliL2NvbnN0YW50cycpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/agentkeepalive/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/agentkeepalive/lib/agent.js":
/*!**************************************************!*\
  !*** ./node_modules/agentkeepalive/lib/agent.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst OriginalAgent = (__webpack_require__(/*! http */ \"http\").Agent);\nconst ms = __webpack_require__(/*! humanize-ms */ \"(rsc)/./node_modules/humanize-ms/index.js\");\nconst debug = (__webpack_require__(/*! util */ \"util\").debuglog)('agentkeepalive');\nconst {\n  INIT_SOCKET,\n  CURRENT_ID,\n  CREATE_ID,\n  SOCKET_CREATED_TIME,\n  SOCKET_NAME,\n  SOCKET_REQUEST_COUNT,\n  SOCKET_REQUEST_FINISHED_COUNT,\n} = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/agentkeepalive/lib/constants.js\");\n\n// OriginalAgent come from\n// - https://github.com/nodejs/node/blob/v8.12.0/lib/_http_agent.js\n// - https://github.com/nodejs/node/blob/v10.12.0/lib/_http_agent.js\n\n// node <= 10\nlet defaultTimeoutListenerCount = 1;\nconst majorVersion = parseInt(process.version.split('.', 1)[0].substring(1));\nif (majorVersion >= 11 && majorVersion <= 12) {\n  defaultTimeoutListenerCount = 2;\n} else if (majorVersion >= 13) {\n  defaultTimeoutListenerCount = 3;\n}\n\nfunction deprecate(message) {\n  console.log('[agentkeepalive:deprecated] %s', message);\n}\n\nclass Agent extends OriginalAgent {\n  constructor(options) {\n    options = options || {};\n    options.keepAlive = options.keepAlive !== false;\n    // default is keep-alive and 4s free socket timeout\n    // see https://medium.com/ssense-tech/reduce-networking-errors-in-nodejs-23b4eb9f2d83\n    if (options.freeSocketTimeout === undefined) {\n      options.freeSocketTimeout = 4000;\n    }\n    // Legacy API: keepAliveTimeout should be rename to `freeSocketTimeout`\n    if (options.keepAliveTimeout) {\n      deprecate('options.keepAliveTimeout is deprecated, please use options.freeSocketTimeout instead');\n      options.freeSocketTimeout = options.keepAliveTimeout;\n      delete options.keepAliveTimeout;\n    }\n    // Legacy API: freeSocketKeepAliveTimeout should be rename to `freeSocketTimeout`\n    if (options.freeSocketKeepAliveTimeout) {\n      deprecate('options.freeSocketKeepAliveTimeout is deprecated, please use options.freeSocketTimeout instead');\n      options.freeSocketTimeout = options.freeSocketKeepAliveTimeout;\n      delete options.freeSocketKeepAliveTimeout;\n    }\n\n    // Sets the socket to timeout after timeout milliseconds of inactivity on the socket.\n    // By default is double free socket timeout.\n    if (options.timeout === undefined) {\n      // make sure socket default inactivity timeout >= 8s\n      options.timeout = Math.max(options.freeSocketTimeout * 2, 8000);\n    }\n\n    // support humanize format\n    options.timeout = ms(options.timeout);\n    options.freeSocketTimeout = ms(options.freeSocketTimeout);\n    options.socketActiveTTL = options.socketActiveTTL ? ms(options.socketActiveTTL) : 0;\n\n    super(options);\n\n    this[CURRENT_ID] = 0;\n\n    // create socket success counter\n    this.createSocketCount = 0;\n    this.createSocketCountLastCheck = 0;\n\n    this.createSocketErrorCount = 0;\n    this.createSocketErrorCountLastCheck = 0;\n\n    this.closeSocketCount = 0;\n    this.closeSocketCountLastCheck = 0;\n\n    // socket error event count\n    this.errorSocketCount = 0;\n    this.errorSocketCountLastCheck = 0;\n\n    // request finished counter\n    this.requestCount = 0;\n    this.requestCountLastCheck = 0;\n\n    // including free socket timeout counter\n    this.timeoutSocketCount = 0;\n    this.timeoutSocketCountLastCheck = 0;\n\n    this.on('free', socket => {\n      // https://github.com/nodejs/node/pull/32000\n      // Node.js native agent will check socket timeout eqs agent.options.timeout.\n      // Use the ttl or freeSocketTimeout to overwrite.\n      const timeout = this.calcSocketTimeout(socket);\n      if (timeout > 0 && socket.timeout !== timeout) {\n        socket.setTimeout(timeout);\n      }\n    });\n  }\n\n  get freeSocketKeepAliveTimeout() {\n    deprecate('agent.freeSocketKeepAliveTimeout is deprecated, please use agent.options.freeSocketTimeout instead');\n    return this.options.freeSocketTimeout;\n  }\n\n  get timeout() {\n    deprecate('agent.timeout is deprecated, please use agent.options.timeout instead');\n    return this.options.timeout;\n  }\n\n  get socketActiveTTL() {\n    deprecate('agent.socketActiveTTL is deprecated, please use agent.options.socketActiveTTL instead');\n    return this.options.socketActiveTTL;\n  }\n\n  calcSocketTimeout(socket) {\n    /**\n     * return <= 0: should free socket\n     * return > 0: should update socket timeout\n     * return undefined: not find custom timeout\n     */\n    let freeSocketTimeout = this.options.freeSocketTimeout;\n    const socketActiveTTL = this.options.socketActiveTTL;\n    if (socketActiveTTL) {\n      // check socketActiveTTL\n      const aliveTime = Date.now() - socket[SOCKET_CREATED_TIME];\n      const diff = socketActiveTTL - aliveTime;\n      if (diff <= 0) {\n        return diff;\n      }\n      if (freeSocketTimeout && diff < freeSocketTimeout) {\n        freeSocketTimeout = diff;\n      }\n    }\n    // set freeSocketTimeout\n    if (freeSocketTimeout) {\n      // set free keepalive timer\n      // try to use socket custom freeSocketTimeout first, support headers['keep-alive']\n      // https://github.com/node-modules/urllib/blob/b76053020923f4d99a1c93cf2e16e0c5ba10bacf/lib/urllib.js#L498\n      const customFreeSocketTimeout = socket.freeSocketTimeout || socket.freeSocketKeepAliveTimeout;\n      return customFreeSocketTimeout || freeSocketTimeout;\n    }\n  }\n\n  keepSocketAlive(socket) {\n    const result = super.keepSocketAlive(socket);\n    // should not keepAlive, do nothing\n    if (!result) return result;\n\n    const customTimeout = this.calcSocketTimeout(socket);\n    if (typeof customTimeout === 'undefined') {\n      return true;\n    }\n    if (customTimeout <= 0) {\n      debug('%s(requests: %s, finished: %s) free but need to destroy by TTL, request count %s, diff is %s',\n        socket[SOCKET_NAME], socket[SOCKET_REQUEST_COUNT], socket[SOCKET_REQUEST_FINISHED_COUNT], customTimeout);\n      return false;\n    }\n    if (socket.timeout !== customTimeout) {\n      socket.setTimeout(customTimeout);\n    }\n    return true;\n  }\n\n  // only call on addRequest\n  reuseSocket(...args) {\n    // reuseSocket(socket, req)\n    super.reuseSocket(...args);\n    const socket = args[0];\n    const req = args[1];\n    req.reusedSocket = true;\n    const agentTimeout = this.options.timeout;\n    if (getSocketTimeout(socket) !== agentTimeout) {\n      // reset timeout before use\n      socket.setTimeout(agentTimeout);\n      debug('%s reset timeout to %sms', socket[SOCKET_NAME], agentTimeout);\n    }\n    socket[SOCKET_REQUEST_COUNT]++;\n    debug('%s(requests: %s, finished: %s) reuse on addRequest, timeout %sms',\n      socket[SOCKET_NAME], socket[SOCKET_REQUEST_COUNT], socket[SOCKET_REQUEST_FINISHED_COUNT],\n      getSocketTimeout(socket));\n  }\n\n  [CREATE_ID]() {\n    const id = this[CURRENT_ID]++;\n    if (this[CURRENT_ID] === Number.MAX_SAFE_INTEGER) this[CURRENT_ID] = 0;\n    return id;\n  }\n\n  [INIT_SOCKET](socket, options) {\n    // bugfix here.\n    // https on node 8, 10 won't set agent.options.timeout by default\n    // TODO: need to fix on node itself\n    if (options.timeout) {\n      const timeout = getSocketTimeout(socket);\n      if (!timeout) {\n        socket.setTimeout(options.timeout);\n      }\n    }\n\n    if (this.options.keepAlive) {\n      // Disable Nagle's algorithm: http://blog.caustik.com/2012/04/08/scaling-node-js-to-100k-concurrent-connections/\n      // https://fengmk2.com/benchmark/nagle-algorithm-delayed-ack-mock.html\n      socket.setNoDelay(true);\n    }\n    this.createSocketCount++;\n    if (this.options.socketActiveTTL) {\n      socket[SOCKET_CREATED_TIME] = Date.now();\n    }\n    // don't show the hole '-----BEGIN CERTIFICATE----' key string\n    socket[SOCKET_NAME] = `sock[${this[CREATE_ID]()}#${options._agentKey}]`.split('-----BEGIN', 1)[0];\n    socket[SOCKET_REQUEST_COUNT] = 1;\n    socket[SOCKET_REQUEST_FINISHED_COUNT] = 0;\n    installListeners(this, socket, options);\n  }\n\n  createConnection(options, oncreate) {\n    let called = false;\n    const onNewCreate = (err, socket) => {\n      if (called) return;\n      called = true;\n\n      if (err) {\n        this.createSocketErrorCount++;\n        return oncreate(err);\n      }\n      this[INIT_SOCKET](socket, options);\n      oncreate(err, socket);\n    };\n\n    const newSocket = super.createConnection(options, onNewCreate);\n    if (newSocket) onNewCreate(null, newSocket);\n    return newSocket;\n  }\n\n  get statusChanged() {\n    const changed = this.createSocketCount !== this.createSocketCountLastCheck ||\n      this.createSocketErrorCount !== this.createSocketErrorCountLastCheck ||\n      this.closeSocketCount !== this.closeSocketCountLastCheck ||\n      this.errorSocketCount !== this.errorSocketCountLastCheck ||\n      this.timeoutSocketCount !== this.timeoutSocketCountLastCheck ||\n      this.requestCount !== this.requestCountLastCheck;\n    if (changed) {\n      this.createSocketCountLastCheck = this.createSocketCount;\n      this.createSocketErrorCountLastCheck = this.createSocketErrorCount;\n      this.closeSocketCountLastCheck = this.closeSocketCount;\n      this.errorSocketCountLastCheck = this.errorSocketCount;\n      this.timeoutSocketCountLastCheck = this.timeoutSocketCount;\n      this.requestCountLastCheck = this.requestCount;\n    }\n    return changed;\n  }\n\n  getCurrentStatus() {\n    return {\n      createSocketCount: this.createSocketCount,\n      createSocketErrorCount: this.createSocketErrorCount,\n      closeSocketCount: this.closeSocketCount,\n      errorSocketCount: this.errorSocketCount,\n      timeoutSocketCount: this.timeoutSocketCount,\n      requestCount: this.requestCount,\n      freeSockets: inspect(this.freeSockets),\n      sockets: inspect(this.sockets),\n      requests: inspect(this.requests),\n    };\n  }\n}\n\n// node 8 don't has timeout attribute on socket\n// https://github.com/nodejs/node/pull/21204/files#diff-e6ef024c3775d787c38487a6309e491dR408\nfunction getSocketTimeout(socket) {\n  return socket.timeout || socket._idleTimeout;\n}\n\nfunction installListeners(agent, socket, options) {\n  debug('%s create, timeout %sms', socket[SOCKET_NAME], getSocketTimeout(socket));\n\n  // listener socket events: close, timeout, error, free\n  function onFree() {\n    // create and socket.emit('free') logic\n    // https://github.com/nodejs/node/blob/master/lib/_http_agent.js#L311\n    // no req on the socket, it should be the new socket\n    if (!socket._httpMessage && socket[SOCKET_REQUEST_COUNT] === 1) return;\n\n    socket[SOCKET_REQUEST_FINISHED_COUNT]++;\n    agent.requestCount++;\n    debug('%s(requests: %s, finished: %s) free',\n      socket[SOCKET_NAME], socket[SOCKET_REQUEST_COUNT], socket[SOCKET_REQUEST_FINISHED_COUNT]);\n\n    // should reuse on pedding requests?\n    const name = agent.getName(options);\n    if (socket.writable && agent.requests[name] && agent.requests[name].length) {\n      // will be reuse on agent free listener\n      socket[SOCKET_REQUEST_COUNT]++;\n      debug('%s(requests: %s, finished: %s) will be reuse on agent free event',\n        socket[SOCKET_NAME], socket[SOCKET_REQUEST_COUNT], socket[SOCKET_REQUEST_FINISHED_COUNT]);\n    }\n  }\n  socket.on('free', onFree);\n\n  function onClose(isError) {\n    debug('%s(requests: %s, finished: %s) close, isError: %s',\n      socket[SOCKET_NAME], socket[SOCKET_REQUEST_COUNT], socket[SOCKET_REQUEST_FINISHED_COUNT], isError);\n    agent.closeSocketCount++;\n  }\n  socket.on('close', onClose);\n\n  // start socket timeout handler\n  function onTimeout() {\n    // onTimeout and emitRequestTimeout(_http_client.js)\n    // https://github.com/nodejs/node/blob/v12.x/lib/_http_client.js#L711\n    const listenerCount = socket.listeners('timeout').length;\n    // node <= 10, default listenerCount is 1, onTimeout\n    // 11 < node <= 12, default listenerCount is 2, onTimeout and emitRequestTimeout\n    // node >= 13, default listenerCount is 3, onTimeout,\n    //   onTimeout(https://github.com/nodejs/node/pull/32000/files#diff-5f7fb0850412c6be189faeddea6c5359R333)\n    //   and emitRequestTimeout\n    const timeout = getSocketTimeout(socket);\n    const req = socket._httpMessage;\n    const reqTimeoutListenerCount = req && req.listeners('timeout').length || 0;\n    debug('%s(requests: %s, finished: %s) timeout after %sms, listeners %s, defaultTimeoutListenerCount %s, hasHttpRequest %s, HttpRequest timeoutListenerCount %s',\n      socket[SOCKET_NAME], socket[SOCKET_REQUEST_COUNT], socket[SOCKET_REQUEST_FINISHED_COUNT],\n      timeout, listenerCount, defaultTimeoutListenerCount, !!req, reqTimeoutListenerCount);\n    if (debug.enabled) {\n      debug('timeout listeners: %s', socket.listeners('timeout').map(f => f.name).join(', '));\n    }\n    agent.timeoutSocketCount++;\n    const name = agent.getName(options);\n    if (agent.freeSockets[name] && agent.freeSockets[name].indexOf(socket) !== -1) {\n      // free socket timeout, destroy quietly\n      socket.destroy();\n      // Remove it from freeSockets list immediately to prevent new requests\n      // from being sent through this socket.\n      agent.removeSocket(socket, options);\n      debug('%s is free, destroy quietly', socket[SOCKET_NAME]);\n    } else {\n      // if there is no any request socket timeout handler,\n      // agent need to handle socket timeout itself.\n      //\n      // custom request socket timeout handle logic must follow these rules:\n      //  1. Destroy socket first\n      //  2. Must emit socket 'agentRemove' event tell agent remove socket\n      //     from freeSockets list immediately.\n      //     Otherise you may be get 'socket hang up' error when reuse\n      //     free socket and timeout happen in the same time.\n      if (reqTimeoutListenerCount === 0) {\n        const error = new Error('Socket timeout');\n        error.code = 'ERR_SOCKET_TIMEOUT';\n        error.timeout = timeout;\n        // must manually call socket.end() or socket.destroy() to end the connection.\n        // https://nodejs.org/dist/latest-v10.x/docs/api/net.html#net_socket_settimeout_timeout_callback\n        socket.destroy(error);\n        agent.removeSocket(socket, options);\n        debug('%s destroy with timeout error', socket[SOCKET_NAME]);\n      }\n    }\n  }\n  socket.on('timeout', onTimeout);\n\n  function onError(err) {\n    const listenerCount = socket.listeners('error').length;\n    debug('%s(requests: %s, finished: %s) error: %s, listenerCount: %s',\n      socket[SOCKET_NAME], socket[SOCKET_REQUEST_COUNT], socket[SOCKET_REQUEST_FINISHED_COUNT],\n      err, listenerCount);\n    agent.errorSocketCount++;\n    if (listenerCount === 1) {\n      // if socket don't contain error event handler, don't catch it, emit it again\n      debug('%s emit uncaught error event', socket[SOCKET_NAME]);\n      socket.removeListener('error', onError);\n      socket.emit('error', err);\n    }\n  }\n  socket.on('error', onError);\n\n  function onRemove() {\n    debug('%s(requests: %s, finished: %s) agentRemove',\n      socket[SOCKET_NAME],\n      socket[SOCKET_REQUEST_COUNT], socket[SOCKET_REQUEST_FINISHED_COUNT]);\n    // We need this function for cases like HTTP 'upgrade'\n    // (defined by WebSockets) where we need to remove a socket from the\n    // pool because it'll be locked up indefinitely\n    socket.removeListener('close', onClose);\n    socket.removeListener('error', onError);\n    socket.removeListener('free', onFree);\n    socket.removeListener('timeout', onTimeout);\n    socket.removeListener('agentRemove', onRemove);\n  }\n  socket.on('agentRemove', onRemove);\n}\n\nmodule.exports = Agent;\n\nfunction inspect(obj) {\n  const res = {};\n  for (const key in obj) {\n    res[key] = obj[key].length;\n  }\n  return res;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/agentkeepalive/lib/agent.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/agentkeepalive/lib/constants.js":
/*!******************************************************!*\
  !*** ./node_modules/agentkeepalive/lib/constants.js ***!
  \******************************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = {\n  // agent\n  CURRENT_ID: Symbol('agentkeepalive#currentId'),\n  CREATE_ID: Symbol('agentkeepalive#createId'),\n  INIT_SOCKET: Symbol('agentkeepalive#initSocket'),\n  CREATE_HTTPS_CONNECTION: Symbol('agentkeepalive#createHttpsConnection'),\n  // socket\n  SOCKET_CREATED_TIME: Symbol('agentkeepalive#socketCreatedTime'),\n  SOCKET_NAME: Symbol('agentkeepalive#socketName'),\n  SOCKET_REQUEST_COUNT: Symbol('agentkeepalive#socketRequestCount'),\n  SOCKET_REQUEST_FINISHED_COUNT: Symbol('agentkeepalive#socketRequestFinishedCount'),\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvYWdlbnRrZWVwYWxpdmUvbGliL2NvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1wYXBlci1yZWFkaW5nLWFzc2lzdGFudC8uL25vZGVfbW9kdWxlcy9hZ2VudGtlZXBhbGl2ZS9saWIvY29uc3RhbnRzLmpzPzllNmEiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5tb2R1bGUuZXhwb3J0cyA9IHtcbiAgLy8gYWdlbnRcbiAgQ1VSUkVOVF9JRDogU3ltYm9sKCdhZ2VudGtlZXBhbGl2ZSNjdXJyZW50SWQnKSxcbiAgQ1JFQVRFX0lEOiBTeW1ib2woJ2FnZW50a2VlcGFsaXZlI2NyZWF0ZUlkJyksXG4gIElOSVRfU09DS0VUOiBTeW1ib2woJ2FnZW50a2VlcGFsaXZlI2luaXRTb2NrZXQnKSxcbiAgQ1JFQVRFX0hUVFBTX0NPTk5FQ1RJT046IFN5bWJvbCgnYWdlbnRrZWVwYWxpdmUjY3JlYXRlSHR0cHNDb25uZWN0aW9uJyksXG4gIC8vIHNvY2tldFxuICBTT0NLRVRfQ1JFQVRFRF9USU1FOiBTeW1ib2woJ2FnZW50a2VlcGFsaXZlI3NvY2tldENyZWF0ZWRUaW1lJyksXG4gIFNPQ0tFVF9OQU1FOiBTeW1ib2woJ2FnZW50a2VlcGFsaXZlI3NvY2tldE5hbWUnKSxcbiAgU09DS0VUX1JFUVVFU1RfQ09VTlQ6IFN5bWJvbCgnYWdlbnRrZWVwYWxpdmUjc29ja2V0UmVxdWVzdENvdW50JyksXG4gIFNPQ0tFVF9SRVFVRVNUX0ZJTklTSEVEX0NPVU5UOiBTeW1ib2woJ2FnZW50a2VlcGFsaXZlI3NvY2tldFJlcXVlc3RGaW5pc2hlZENvdW50JyksXG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/agentkeepalive/lib/constants.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/agentkeepalive/lib/https_agent.js":
/*!********************************************************!*\
  !*** ./node_modules/agentkeepalive/lib/https_agent.js ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst OriginalHttpsAgent = (__webpack_require__(/*! https */ \"https\").Agent);\nconst HttpAgent = __webpack_require__(/*! ./agent */ \"(rsc)/./node_modules/agentkeepalive/lib/agent.js\");\nconst {\n  INIT_SOCKET,\n  CREATE_HTTPS_CONNECTION,\n} = __webpack_require__(/*! ./constants */ \"(rsc)/./node_modules/agentkeepalive/lib/constants.js\");\n\nclass HttpsAgent extends HttpAgent {\n  constructor(options) {\n    super(options);\n\n    this.defaultPort = 443;\n    this.protocol = 'https:';\n    this.maxCachedSessions = this.options.maxCachedSessions;\n    /* istanbul ignore next */\n    if (this.maxCachedSessions === undefined) {\n      this.maxCachedSessions = 100;\n    }\n\n    this._sessionCache = {\n      map: {},\n      list: [],\n    };\n  }\n\n  createConnection(options, oncreate) {\n    const socket = this[CREATE_HTTPS_CONNECTION](options, oncreate);\n    this[INIT_SOCKET](socket, options);\n    return socket;\n  }\n}\n\n// https://github.com/nodejs/node/blob/master/lib/https.js#L89\nHttpsAgent.prototype[CREATE_HTTPS_CONNECTION] = OriginalHttpsAgent.prototype.createConnection;\n\n[\n  'getName',\n  '_getSession',\n  '_cacheSession',\n  // https://github.com/nodejs/node/pull/4982\n  '_evictSession',\n].forEach(function(method) {\n  /* istanbul ignore next */\n  if (typeof OriginalHttpsAgent.prototype[method] === 'function') {\n    HttpsAgent.prototype[method] = OriginalHttpsAgent.prototype[method];\n  }\n});\n\nmodule.exports = HttpsAgent;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/agentkeepalive/lib/https_agent.js\n");

/***/ })

};
;