"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@sinclair";
exports.ids = ["vendor-chunks/@sinclair"];
exports.modules = {

/***/ "(rsc)/./node_modules/@sinclair/typebox/typebox.js":
/*!***************************************************!*\
  !*** ./node_modules/@sinclair/typebox/typebox.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/*--------------------------------------------------------------------------\n\n@sinclair/typebox\n\nThe MIT License (MIT)\n\nCopyright (c) 2017-2023 Haydn Paterson (sinclair) <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n\n---------------------------------------------------------------------------*/\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Type = exports.StandardType = exports.ExtendedTypeBuilder = exports.StandardTypeBuilder = exports.TypeBuilder = exports.TemplateLiteralDslParser = exports.TemplateLiteralGenerator = exports.TemplateLiteralFinite = exports.TemplateLiteralParser = exports.TemplateLiteralParserError = exports.TemplateLiteralResolver = exports.TemplateLiteralPattern = exports.UnionResolver = exports.KeyArrayResolver = exports.KeyResolver = exports.ObjectMap = exports.IndexedAccessor = exports.TypeClone = exports.TypeExtends = exports.TypeExtendsResult = exports.ExtendsUndefined = exports.TypeGuard = exports.TypeGuardUnknownTypeError = exports.FormatRegistry = exports.TypeRegistry = exports.PatternStringExact = exports.PatternNumberExact = exports.PatternBooleanExact = exports.PatternString = exports.PatternNumber = exports.PatternBoolean = exports.Kind = exports.Hint = exports.Modifier = void 0;\n// --------------------------------------------------------------------------\n// Symbols\n// --------------------------------------------------------------------------\nexports.Modifier = Symbol.for('TypeBox.Modifier');\nexports.Hint = Symbol.for('TypeBox.Hint');\nexports.Kind = Symbol.for('TypeBox.Kind');\n// --------------------------------------------------------------------------\n// Patterns\n// --------------------------------------------------------------------------\nexports.PatternBoolean = '(true|false)';\nexports.PatternNumber = '(0|[1-9][0-9]*)';\nexports.PatternString = '(.*)';\nexports.PatternBooleanExact = `^${exports.PatternBoolean}$`;\nexports.PatternNumberExact = `^${exports.PatternNumber}$`;\nexports.PatternStringExact = `^${exports.PatternString}$`;\n/** A registry for user defined types */\nvar TypeRegistry;\n(function (TypeRegistry) {\n    const map = new Map();\n    /** Returns the entries in this registry */\n    function Entries() {\n        return new Map(map);\n    }\n    TypeRegistry.Entries = Entries;\n    /** Clears all user defined types */\n    function Clear() {\n        return map.clear();\n    }\n    TypeRegistry.Clear = Clear;\n    /** Returns true if this registry contains this kind */\n    function Has(kind) {\n        return map.has(kind);\n    }\n    TypeRegistry.Has = Has;\n    /** Sets a validation function for a user defined type */\n    function Set(kind, func) {\n        map.set(kind, func);\n    }\n    TypeRegistry.Set = Set;\n    /** Gets a custom validation function for a user defined type */\n    function Get(kind) {\n        return map.get(kind);\n    }\n    TypeRegistry.Get = Get;\n})(TypeRegistry || (exports.TypeRegistry = TypeRegistry = {}));\n/** A registry for user defined string formats */\nvar FormatRegistry;\n(function (FormatRegistry) {\n    const map = new Map();\n    /** Returns the entries in this registry */\n    function Entries() {\n        return new Map(map);\n    }\n    FormatRegistry.Entries = Entries;\n    /** Clears all user defined string formats */\n    function Clear() {\n        return map.clear();\n    }\n    FormatRegistry.Clear = Clear;\n    /** Returns true if the user defined string format exists */\n    function Has(format) {\n        return map.has(format);\n    }\n    FormatRegistry.Has = Has;\n    /** Sets a validation function for a user defined string format */\n    function Set(format, func) {\n        map.set(format, func);\n    }\n    FormatRegistry.Set = Set;\n    /** Gets a validation function for a user defined string format */\n    function Get(format) {\n        return map.get(format);\n    }\n    FormatRegistry.Get = Get;\n})(FormatRegistry || (exports.FormatRegistry = FormatRegistry = {}));\n// --------------------------------------------------------------------------\n// TypeGuard\n// --------------------------------------------------------------------------\nclass TypeGuardUnknownTypeError extends Error {\n    constructor(schema) {\n        super('TypeGuard: Unknown type');\n        this.schema = schema;\n    }\n}\nexports.TypeGuardUnknownTypeError = TypeGuardUnknownTypeError;\n/** Provides functions to test if JavaScript values are TypeBox types */\nvar TypeGuard;\n(function (TypeGuard) {\n    function IsObject(value) {\n        return typeof value === 'object' && value !== null && !Array.isArray(value);\n    }\n    function IsArray(value) {\n        return typeof value === 'object' && value !== null && Array.isArray(value);\n    }\n    function IsPattern(value) {\n        try {\n            new RegExp(value);\n            return true;\n        }\n        catch {\n            return false;\n        }\n    }\n    function IsControlCharacterFree(value) {\n        if (typeof value !== 'string')\n            return false;\n        for (let i = 0; i < value.length; i++) {\n            const code = value.charCodeAt(i);\n            if ((code >= 7 && code <= 13) || code === 27 || code === 127) {\n                return false;\n            }\n        }\n        return true;\n    }\n    function IsAdditionalProperties(value) {\n        return IsOptionalBoolean(value) || TSchema(value);\n    }\n    function IsBigInt(value) {\n        return typeof value === 'bigint';\n    }\n    function IsString(value) {\n        return typeof value === 'string';\n    }\n    function IsNumber(value) {\n        return typeof value === 'number' && globalThis.Number.isFinite(value);\n    }\n    function IsBoolean(value) {\n        return typeof value === 'boolean';\n    }\n    function IsOptionalBigInt(value) {\n        return value === undefined || (value !== undefined && IsBigInt(value));\n    }\n    function IsOptionalNumber(value) {\n        return value === undefined || (value !== undefined && IsNumber(value));\n    }\n    function IsOptionalBoolean(value) {\n        return value === undefined || (value !== undefined && IsBoolean(value));\n    }\n    function IsOptionalString(value) {\n        return value === undefined || (value !== undefined && IsString(value));\n    }\n    function IsOptionalPattern(value) {\n        return value === undefined || (value !== undefined && IsString(value) && IsControlCharacterFree(value) && IsPattern(value));\n    }\n    function IsOptionalFormat(value) {\n        return value === undefined || (value !== undefined && IsString(value) && IsControlCharacterFree(value));\n    }\n    function IsOptionalSchema(value) {\n        return value === undefined || TSchema(value);\n    }\n    /** Returns true if the given schema is TAny */\n    function TAny(schema) {\n        return TKind(schema) && schema[exports.Kind] === 'Any' && IsOptionalString(schema.$id);\n    }\n    TypeGuard.TAny = TAny;\n    /** Returns true if the given schema is TArray */\n    function TArray(schema) {\n        return (TKind(schema) &&\n            schema[exports.Kind] === 'Array' &&\n            schema.type === 'array' &&\n            IsOptionalString(schema.$id) &&\n            TSchema(schema.items) &&\n            IsOptionalNumber(schema.minItems) &&\n            IsOptionalNumber(schema.maxItems) &&\n            IsOptionalBoolean(schema.uniqueItems));\n    }\n    TypeGuard.TArray = TArray;\n    /** Returns true if the given schema is TBigInt */\n    function TBigInt(schema) {\n        // prettier-ignore\n        return (TKind(schema) &&\n            schema[exports.Kind] === 'BigInt' &&\n            schema.type === 'null' &&\n            schema.typeOf === 'BigInt' &&\n            IsOptionalString(schema.$id) &&\n            IsOptionalBigInt(schema.multipleOf) &&\n            IsOptionalBigInt(schema.minimum) &&\n            IsOptionalBigInt(schema.maximum) &&\n            IsOptionalBigInt(schema.exclusiveMinimum) &&\n            IsOptionalBigInt(schema.exclusiveMaximum));\n    }\n    TypeGuard.TBigInt = TBigInt;\n    /** Returns true if the given schema is TBoolean */\n    function TBoolean(schema) {\n        // prettier-ignore\n        return (TKind(schema) &&\n            schema[exports.Kind] === 'Boolean' &&\n            schema.type === 'boolean' &&\n            IsOptionalString(schema.$id));\n    }\n    TypeGuard.TBoolean = TBoolean;\n    /** Returns true if the given schema is TConstructor */\n    function TConstructor(schema) {\n        // prettier-ignore\n        if (!(TKind(schema) &&\n            schema[exports.Kind] === 'Constructor' &&\n            schema.type === 'object' &&\n            schema.instanceOf === 'Constructor' &&\n            IsOptionalString(schema.$id) &&\n            IsArray(schema.parameters) &&\n            TSchema(schema.returns))) {\n            return false;\n        }\n        for (const parameter of schema.parameters) {\n            if (!TSchema(parameter))\n                return false;\n        }\n        return true;\n    }\n    TypeGuard.TConstructor = TConstructor;\n    /** Returns true if the given schema is TDate */\n    function TDate(schema) {\n        return (TKind(schema) &&\n            schema[exports.Kind] === 'Date' &&\n            schema.type === 'object' &&\n            schema.instanceOf === 'Date' &&\n            IsOptionalString(schema.$id) &&\n            IsOptionalNumber(schema.minimumTimestamp) &&\n            IsOptionalNumber(schema.maximumTimestamp) &&\n            IsOptionalNumber(schema.exclusiveMinimumTimestamp) &&\n            IsOptionalNumber(schema.exclusiveMaximumTimestamp));\n    }\n    TypeGuard.TDate = TDate;\n    /** Returns true if the given schema is TFunction */\n    function TFunction(schema) {\n        // prettier-ignore\n        if (!(TKind(schema) &&\n            schema[exports.Kind] === 'Function' &&\n            schema.type === 'object' &&\n            schema.instanceOf === 'Function' &&\n            IsOptionalString(schema.$id) &&\n            IsArray(schema.parameters) &&\n            TSchema(schema.returns))) {\n            return false;\n        }\n        for (const parameter of schema.parameters) {\n            if (!TSchema(parameter))\n                return false;\n        }\n        return true;\n    }\n    TypeGuard.TFunction = TFunction;\n    /** Returns true if the given schema is TInteger */\n    function TInteger(schema) {\n        return (TKind(schema) &&\n            schema[exports.Kind] === 'Integer' &&\n            schema.type === 'integer' &&\n            IsOptionalString(schema.$id) &&\n            IsOptionalNumber(schema.multipleOf) &&\n            IsOptionalNumber(schema.minimum) &&\n            IsOptionalNumber(schema.maximum) &&\n            IsOptionalNumber(schema.exclusiveMinimum) &&\n            IsOptionalNumber(schema.exclusiveMaximum));\n    }\n    TypeGuard.TInteger = TInteger;\n    /** Returns true if the given schema is TIntersect */\n    function TIntersect(schema) {\n        // prettier-ignore\n        if (!(TKind(schema) &&\n            schema[exports.Kind] === 'Intersect' &&\n            IsArray(schema.allOf) &&\n            IsOptionalString(schema.type) &&\n            (IsOptionalBoolean(schema.unevaluatedProperties) || IsOptionalSchema(schema.unevaluatedProperties)) &&\n            IsOptionalString(schema.$id))) {\n            return false;\n        }\n        if ('type' in schema && schema.type !== 'object') {\n            return false;\n        }\n        for (const inner of schema.allOf) {\n            if (!TSchema(inner))\n                return false;\n        }\n        return true;\n    }\n    TypeGuard.TIntersect = TIntersect;\n    /** Returns true if the given schema is TKind */\n    function TKind(schema) {\n        return IsObject(schema) && exports.Kind in schema && typeof schema[exports.Kind] === 'string'; // TS 4.1.5: any required for symbol indexer\n    }\n    TypeGuard.TKind = TKind;\n    /** Returns true if the given schema is TLiteral<string> */\n    function TLiteralString(schema) {\n        return TKind(schema) && schema[exports.Kind] === 'Literal' && IsOptionalString(schema.$id) && typeof schema.const === 'string';\n    }\n    TypeGuard.TLiteralString = TLiteralString;\n    /** Returns true if the given schema is TLiteral<number> */\n    function TLiteralNumber(schema) {\n        return TKind(schema) && schema[exports.Kind] === 'Literal' && IsOptionalString(schema.$id) && typeof schema.const === 'number';\n    }\n    TypeGuard.TLiteralNumber = TLiteralNumber;\n    /** Returns true if the given schema is TLiteral<boolean> */\n    function TLiteralBoolean(schema) {\n        return TKind(schema) && schema[exports.Kind] === 'Literal' && IsOptionalString(schema.$id) && typeof schema.const === 'boolean';\n    }\n    TypeGuard.TLiteralBoolean = TLiteralBoolean;\n    /** Returns true if the given schema is TLiteral */\n    function TLiteral(schema) {\n        return TLiteralString(schema) || TLiteralNumber(schema) || TLiteralBoolean(schema);\n    }\n    TypeGuard.TLiteral = TLiteral;\n    /** Returns true if the given schema is TNever */\n    function TNever(schema) {\n        return TKind(schema) && schema[exports.Kind] === 'Never' && IsObject(schema.not) && globalThis.Object.getOwnPropertyNames(schema.not).length === 0;\n    }\n    TypeGuard.TNever = TNever;\n    /** Returns true if the given schema is TNot */\n    function TNot(schema) {\n        // prettier-ignore\n        return (TKind(schema) &&\n            schema[exports.Kind] === 'Not' &&\n            TSchema(schema.not));\n    }\n    TypeGuard.TNot = TNot;\n    /** Returns true if the given schema is TNull */\n    function TNull(schema) {\n        // prettier-ignore\n        return (TKind(schema) &&\n            schema[exports.Kind] === 'Null' &&\n            schema.type === 'null' &&\n            IsOptionalString(schema.$id));\n    }\n    TypeGuard.TNull = TNull;\n    /** Returns true if the given schema is TNumber */\n    function TNumber(schema) {\n        return (TKind(schema) &&\n            schema[exports.Kind] === 'Number' &&\n            schema.type === 'number' &&\n            IsOptionalString(schema.$id) &&\n            IsOptionalNumber(schema.multipleOf) &&\n            IsOptionalNumber(schema.minimum) &&\n            IsOptionalNumber(schema.maximum) &&\n            IsOptionalNumber(schema.exclusiveMinimum) &&\n            IsOptionalNumber(schema.exclusiveMaximum));\n    }\n    TypeGuard.TNumber = TNumber;\n    /** Returns true if the given schema is TObject */\n    function TObject(schema) {\n        if (!(TKind(schema) &&\n            schema[exports.Kind] === 'Object' &&\n            schema.type === 'object' &&\n            IsOptionalString(schema.$id) &&\n            IsObject(schema.properties) &&\n            IsAdditionalProperties(schema.additionalProperties) &&\n            IsOptionalNumber(schema.minProperties) &&\n            IsOptionalNumber(schema.maxProperties))) {\n            return false;\n        }\n        for (const [key, value] of Object.entries(schema.properties)) {\n            if (!IsControlCharacterFree(key))\n                return false;\n            if (!TSchema(value))\n                return false;\n        }\n        return true;\n    }\n    TypeGuard.TObject = TObject;\n    /** Returns true if the given schema is TPromise */\n    function TPromise(schema) {\n        // prettier-ignore\n        return (TKind(schema) &&\n            schema[exports.Kind] === 'Promise' &&\n            schema.type === 'object' &&\n            schema.instanceOf === 'Promise' &&\n            IsOptionalString(schema.$id) &&\n            TSchema(schema.item));\n    }\n    TypeGuard.TPromise = TPromise;\n    /** Returns true if the given schema is TRecord */\n    function TRecord(schema) {\n        // prettier-ignore\n        if (!(TKind(schema) &&\n            schema[exports.Kind] === 'Record' &&\n            schema.type === 'object' &&\n            IsOptionalString(schema.$id) &&\n            IsAdditionalProperties(schema.additionalProperties) &&\n            IsObject(schema.patternProperties))) {\n            return false;\n        }\n        const keys = Object.keys(schema.patternProperties);\n        if (keys.length !== 1) {\n            return false;\n        }\n        if (!IsPattern(keys[0])) {\n            return false;\n        }\n        if (!TSchema(schema.patternProperties[keys[0]])) {\n            return false;\n        }\n        return true;\n    }\n    TypeGuard.TRecord = TRecord;\n    /** Returns true if the given schema is TRef */\n    function TRef(schema) {\n        // prettier-ignore\n        return (TKind(schema) &&\n            schema[exports.Kind] === 'Ref' &&\n            IsOptionalString(schema.$id) &&\n            IsString(schema.$ref));\n    }\n    TypeGuard.TRef = TRef;\n    /** Returns true if the given schema is TString */\n    function TString(schema) {\n        return (TKind(schema) &&\n            schema[exports.Kind] === 'String' &&\n            schema.type === 'string' &&\n            IsOptionalString(schema.$id) &&\n            IsOptionalNumber(schema.minLength) &&\n            IsOptionalNumber(schema.maxLength) &&\n            IsOptionalPattern(schema.pattern) &&\n            IsOptionalFormat(schema.format));\n    }\n    TypeGuard.TString = TString;\n    /** Returns true if the given schema is TSymbol */\n    function TSymbol(schema) {\n        // prettier-ignore\n        return (TKind(schema) &&\n            schema[exports.Kind] === 'Symbol' &&\n            schema.type === 'null' &&\n            schema.typeOf === 'Symbol' &&\n            IsOptionalString(schema.$id));\n    }\n    TypeGuard.TSymbol = TSymbol;\n    /** Returns true if the given schema is TTemplateLiteral */\n    function TTemplateLiteral(schema) {\n        // prettier-ignore\n        return (TKind(schema) &&\n            schema[exports.Kind] === 'TemplateLiteral' &&\n            schema.type === 'string' &&\n            IsString(schema.pattern) &&\n            schema.pattern[0] === '^' &&\n            schema.pattern[schema.pattern.length - 1] === '$');\n    }\n    TypeGuard.TTemplateLiteral = TTemplateLiteral;\n    /** Returns true if the given schema is TThis */\n    function TThis(schema) {\n        // prettier-ignore\n        return (TKind(schema) &&\n            schema[exports.Kind] === 'This' &&\n            IsOptionalString(schema.$id) &&\n            IsString(schema.$ref));\n    }\n    TypeGuard.TThis = TThis;\n    /** Returns true if the given schema is TTuple */\n    function TTuple(schema) {\n        // prettier-ignore\n        if (!(TKind(schema) &&\n            schema[exports.Kind] === 'Tuple' &&\n            schema.type === 'array' &&\n            IsOptionalString(schema.$id) &&\n            IsNumber(schema.minItems) &&\n            IsNumber(schema.maxItems) &&\n            schema.minItems === schema.maxItems)) {\n            return false;\n        }\n        if (schema.items === undefined && schema.additionalItems === undefined && schema.minItems === 0) {\n            return true;\n        }\n        if (!IsArray(schema.items)) {\n            return false;\n        }\n        for (const inner of schema.items) {\n            if (!TSchema(inner))\n                return false;\n        }\n        return true;\n    }\n    TypeGuard.TTuple = TTuple;\n    /** Returns true if the given schema is TUndefined */\n    function TUndefined(schema) {\n        // prettier-ignore\n        return (TKind(schema) &&\n            schema[exports.Kind] === 'Undefined' &&\n            schema.type === 'null' &&\n            schema.typeOf === 'Undefined' &&\n            IsOptionalString(schema.$id));\n    }\n    TypeGuard.TUndefined = TUndefined;\n    /** Returns true if the given schema is TUnion<Literal<string | number>[]> */\n    function TUnionLiteral(schema) {\n        return TUnion(schema) && schema.anyOf.every((schema) => TLiteralString(schema) || TLiteralNumber(schema));\n    }\n    TypeGuard.TUnionLiteral = TUnionLiteral;\n    /** Returns true if the given schema is TUnion */\n    function TUnion(schema) {\n        // prettier-ignore\n        if (!(TKind(schema) &&\n            schema[exports.Kind] === 'Union' &&\n            IsArray(schema.anyOf) &&\n            IsOptionalString(schema.$id))) {\n            return false;\n        }\n        for (const inner of schema.anyOf) {\n            if (!TSchema(inner))\n                return false;\n        }\n        return true;\n    }\n    TypeGuard.TUnion = TUnion;\n    /** Returns true if the given schema is TUint8Array */\n    function TUint8Array(schema) {\n        return TKind(schema) && schema[exports.Kind] === 'Uint8Array' && schema.type === 'object' && IsOptionalString(schema.$id) && schema.instanceOf === 'Uint8Array' && IsOptionalNumber(schema.minByteLength) && IsOptionalNumber(schema.maxByteLength);\n    }\n    TypeGuard.TUint8Array = TUint8Array;\n    /** Returns true if the given schema is TUnknown */\n    function TUnknown(schema) {\n        // prettier-ignore\n        return (TKind(schema) &&\n            schema[exports.Kind] === 'Unknown' &&\n            IsOptionalString(schema.$id));\n    }\n    TypeGuard.TUnknown = TUnknown;\n    /** Returns true if the given schema is a raw TUnsafe */\n    function TUnsafe(schema) {\n        // prettier-ignore\n        return (TKind(schema) &&\n            schema[exports.Kind] === 'Unsafe');\n    }\n    TypeGuard.TUnsafe = TUnsafe;\n    /** Returns true if the given schema is TVoid */\n    function TVoid(schema) {\n        // prettier-ignore\n        return (TKind(schema) &&\n            schema[exports.Kind] === 'Void' &&\n            schema.type === 'null' &&\n            schema.typeOf === 'Void' &&\n            IsOptionalString(schema.$id));\n    }\n    TypeGuard.TVoid = TVoid;\n    /** Returns true if this schema has the ReadonlyOptional modifier */\n    function TReadonlyOptional(schema) {\n        return IsObject(schema) && schema[exports.Modifier] === 'ReadonlyOptional';\n    }\n    TypeGuard.TReadonlyOptional = TReadonlyOptional;\n    /** Returns true if this schema has the Readonly modifier */\n    function TReadonly(schema) {\n        return IsObject(schema) && schema[exports.Modifier] === 'Readonly';\n    }\n    TypeGuard.TReadonly = TReadonly;\n    /** Returns true if this schema has the Optional modifier */\n    function TOptional(schema) {\n        return IsObject(schema) && schema[exports.Modifier] === 'Optional';\n    }\n    TypeGuard.TOptional = TOptional;\n    /** Returns true if the given schema is TSchema */\n    function TSchema(schema) {\n        return (typeof schema === 'object' &&\n            (TAny(schema) ||\n                TArray(schema) ||\n                TBoolean(schema) ||\n                TBigInt(schema) ||\n                TConstructor(schema) ||\n                TDate(schema) ||\n                TFunction(schema) ||\n                TInteger(schema) ||\n                TIntersect(schema) ||\n                TLiteral(schema) ||\n                TNever(schema) ||\n                TNot(schema) ||\n                TNull(schema) ||\n                TNumber(schema) ||\n                TObject(schema) ||\n                TPromise(schema) ||\n                TRecord(schema) ||\n                TRef(schema) ||\n                TString(schema) ||\n                TSymbol(schema) ||\n                TTemplateLiteral(schema) ||\n                TThis(schema) ||\n                TTuple(schema) ||\n                TUndefined(schema) ||\n                TUnion(schema) ||\n                TUint8Array(schema) ||\n                TUnknown(schema) ||\n                TUnsafe(schema) ||\n                TVoid(schema) ||\n                (TKind(schema) && TypeRegistry.Has(schema[exports.Kind]))));\n    }\n    TypeGuard.TSchema = TSchema;\n})(TypeGuard || (exports.TypeGuard = TypeGuard = {}));\n// --------------------------------------------------------------------------\n// ExtendsUndefined\n// --------------------------------------------------------------------------\n/** Fast undefined check used for properties of type undefined */\nvar ExtendsUndefined;\n(function (ExtendsUndefined) {\n    function Check(schema) {\n        if (schema[exports.Kind] === 'Undefined')\n            return true;\n        if (schema[exports.Kind] === 'Not') {\n            return !Check(schema.not);\n        }\n        if (schema[exports.Kind] === 'Intersect') {\n            const intersect = schema;\n            return intersect.allOf.every((schema) => Check(schema));\n        }\n        if (schema[exports.Kind] === 'Union') {\n            const union = schema;\n            return union.anyOf.some((schema) => Check(schema));\n        }\n        return false;\n    }\n    ExtendsUndefined.Check = Check;\n})(ExtendsUndefined || (exports.ExtendsUndefined = ExtendsUndefined = {}));\n// --------------------------------------------------------------------------\n// TypeExtends\n// --------------------------------------------------------------------------\nvar TypeExtendsResult;\n(function (TypeExtendsResult) {\n    TypeExtendsResult[TypeExtendsResult[\"Union\"] = 0] = \"Union\";\n    TypeExtendsResult[TypeExtendsResult[\"True\"] = 1] = \"True\";\n    TypeExtendsResult[TypeExtendsResult[\"False\"] = 2] = \"False\";\n})(TypeExtendsResult || (exports.TypeExtendsResult = TypeExtendsResult = {}));\nvar TypeExtends;\n(function (TypeExtends) {\n    // --------------------------------------------------------------------------\n    // IntoBooleanResult\n    // --------------------------------------------------------------------------\n    function IntoBooleanResult(result) {\n        return result === TypeExtendsResult.False ? TypeExtendsResult.False : TypeExtendsResult.True;\n    }\n    // --------------------------------------------------------------------------\n    // Any\n    // --------------------------------------------------------------------------\n    function AnyRight(left, right) {\n        return TypeExtendsResult.True;\n    }\n    function Any(left, right) {\n        if (TypeGuard.TIntersect(right))\n            return IntersectRight(left, right);\n        if (TypeGuard.TUnion(right) && right.anyOf.some((schema) => TypeGuard.TAny(schema) || TypeGuard.TUnknown(schema)))\n            return TypeExtendsResult.True;\n        if (TypeGuard.TUnion(right))\n            return TypeExtendsResult.Union;\n        if (TypeGuard.TUnknown(right))\n            return TypeExtendsResult.True;\n        if (TypeGuard.TAny(right))\n            return TypeExtendsResult.True;\n        return TypeExtendsResult.Union;\n    }\n    // --------------------------------------------------------------------------\n    // Array\n    // --------------------------------------------------------------------------\n    function ArrayRight(left, right) {\n        if (TypeGuard.TUnknown(left))\n            return TypeExtendsResult.False;\n        if (TypeGuard.TAny(left))\n            return TypeExtendsResult.Union;\n        if (TypeGuard.TNever(left))\n            return TypeExtendsResult.True;\n        return TypeExtendsResult.False;\n    }\n    function Array(left, right) {\n        if (TypeGuard.TIntersect(right))\n            return IntersectRight(left, right);\n        if (TypeGuard.TUnion(right))\n            return UnionRight(left, right);\n        if (TypeGuard.TUnknown(right))\n            return UnknownRight(left, right);\n        if (TypeGuard.TAny(right))\n            return AnyRight(left, right);\n        if (TypeGuard.TObject(right) && IsObjectArrayLike(right))\n            return TypeExtendsResult.True;\n        if (!TypeGuard.TArray(right))\n            return TypeExtendsResult.False;\n        return IntoBooleanResult(Visit(left.items, right.items));\n    }\n    // --------------------------------------------------------------------------\n    // BigInt\n    // --------------------------------------------------------------------------\n    function BigInt(left, right) {\n        if (TypeGuard.TIntersect(right))\n            return IntersectRight(left, right);\n        if (TypeGuard.TUnion(right))\n            return UnionRight(left, right);\n        if (TypeGuard.TNever(right))\n            return NeverRight(left, right);\n        if (TypeGuard.TUnknown(right))\n            return UnknownRight(left, right);\n        if (TypeGuard.TAny(right))\n            return AnyRight(left, right);\n        if (TypeGuard.TObject(right))\n            return ObjectRight(left, right);\n        if (TypeGuard.TRecord(right))\n            return RecordRight(left, right);\n        return TypeGuard.TBigInt(right) ? TypeExtendsResult.True : TypeExtendsResult.False;\n    }\n    // --------------------------------------------------------------------------\n    // Boolean\n    // --------------------------------------------------------------------------\n    function BooleanRight(left, right) {\n        if (TypeGuard.TLiteral(left) && typeof left.const === 'boolean')\n            return TypeExtendsResult.True;\n        return TypeGuard.TBoolean(left) ? TypeExtendsResult.True : TypeExtendsResult.False;\n    }\n    function Boolean(left, right) {\n        if (TypeGuard.TIntersect(right))\n            return IntersectRight(left, right);\n        if (TypeGuard.TUnion(right))\n            return UnionRight(left, right);\n        if (TypeGuard.TNever(right))\n            return NeverRight(left, right);\n        if (TypeGuard.TUnknown(right))\n            return UnknownRight(left, right);\n        if (TypeGuard.TAny(right))\n            return AnyRight(left, right);\n        if (TypeGuard.TObject(right))\n            return ObjectRight(left, right);\n        if (TypeGuard.TRecord(right))\n            return RecordRight(left, right);\n        return TypeGuard.TBoolean(right) ? TypeExtendsResult.True : TypeExtendsResult.False;\n    }\n    // --------------------------------------------------------------------------\n    // Constructor\n    // --------------------------------------------------------------------------\n    function Constructor(left, right) {\n        if (TypeGuard.TIntersect(right))\n            return IntersectRight(left, right);\n        if (TypeGuard.TUnion(right))\n            return UnionRight(left, right);\n        if (TypeGuard.TUnknown(right))\n            return UnknownRight(left, right);\n        if (TypeGuard.TAny(right))\n            return AnyRight(left, right);\n        if (TypeGuard.TObject(right))\n            return ObjectRight(left, right);\n        if (!TypeGuard.TConstructor(right))\n            return TypeExtendsResult.False;\n        if (left.parameters.length > right.parameters.length)\n            return TypeExtendsResult.False;\n        if (!left.parameters.every((schema, index) => IntoBooleanResult(Visit(right.parameters[index], schema)) === TypeExtendsResult.True)) {\n            return TypeExtendsResult.False;\n        }\n        return IntoBooleanResult(Visit(left.returns, right.returns));\n    }\n    // --------------------------------------------------------------------------\n    // Date\n    // --------------------------------------------------------------------------\n    function Date(left, right) {\n        if (TypeGuard.TIntersect(right))\n            return IntersectRight(left, right);\n        if (TypeGuard.TUnion(right))\n            return UnionRight(left, right);\n        if (TypeGuard.TUnknown(right))\n            return UnknownRight(left, right);\n        if (TypeGuard.TAny(right))\n            return AnyRight(left, right);\n        if (TypeGuard.TObject(right))\n            return ObjectRight(left, right);\n        if (TypeGuard.TRecord(right))\n            return RecordRight(left, right);\n        return TypeGuard.TDate(right) ? TypeExtendsResult.True : TypeExtendsResult.False;\n    }\n    // --------------------------------------------------------------------------\n    // Function\n    // --------------------------------------------------------------------------\n    function Function(left, right) {\n        if (TypeGuard.TIntersect(right))\n            return IntersectRight(left, right);\n        if (TypeGuard.TUnion(right))\n            return UnionRight(left, right);\n        if (TypeGuard.TUnknown(right))\n            return UnknownRight(left, right);\n        if (TypeGuard.TAny(right))\n            return AnyRight(left, right);\n        if (TypeGuard.TObject(right))\n            return ObjectRight(left, right);\n        if (!TypeGuard.TFunction(right))\n            return TypeExtendsResult.False;\n        if (left.parameters.length > right.parameters.length)\n            return TypeExtendsResult.False;\n        if (!left.parameters.every((schema, index) => IntoBooleanResult(Visit(right.parameters[index], schema)) === TypeExtendsResult.True)) {\n            return TypeExtendsResult.False;\n        }\n        return IntoBooleanResult(Visit(left.returns, right.returns));\n    }\n    // --------------------------------------------------------------------------\n    // Integer\n    // --------------------------------------------------------------------------\n    function IntegerRight(left, right) {\n        if (TypeGuard.TLiteral(left) && typeof left.const === 'number')\n            return TypeExtendsResult.True;\n        return TypeGuard.TNumber(left) || TypeGuard.TInteger(left) ? TypeExtendsResult.True : TypeExtendsResult.False;\n    }\n    function Integer(left, right) {\n        if (TypeGuard.TIntersect(right))\n            return IntersectRight(left, right);\n        if (TypeGuard.TUnion(right))\n            return UnionRight(left, right);\n        if (TypeGuard.TNever(right))\n            return NeverRight(left, right);\n        if (TypeGuard.TUnknown(right))\n            return UnknownRight(left, right);\n        if (TypeGuard.TAny(right))\n            return AnyRight(left, right);\n        if (TypeGuard.TObject(right))\n            return ObjectRight(left, right);\n        if (TypeGuard.TRecord(right))\n            return RecordRight(left, right);\n        return TypeGuard.TInteger(right) || TypeGuard.TNumber(right) ? TypeExtendsResult.True : TypeExtendsResult.False;\n    }\n    // --------------------------------------------------------------------------\n    // Intersect\n    // --------------------------------------------------------------------------\n    function IntersectRight(left, right) {\n        return right.allOf.every((schema) => Visit(left, schema) === TypeExtendsResult.True) ? TypeExtendsResult.True : TypeExtendsResult.False;\n    }\n    function Intersect(left, right) {\n        return left.allOf.some((schema) => Visit(schema, right) === TypeExtendsResult.True) ? TypeExtendsResult.True : TypeExtendsResult.False;\n    }\n    // --------------------------------------------------------------------------\n    // Literal\n    // --------------------------------------------------------------------------\n    function IsLiteralString(schema) {\n        return typeof schema.const === 'string';\n    }\n    function IsLiteralNumber(schema) {\n        return typeof schema.const === 'number';\n    }\n    function IsLiteralBoolean(schema) {\n        return typeof schema.const === 'boolean';\n    }\n    function Literal(left, right) {\n        if (TypeGuard.TIntersect(right))\n            return IntersectRight(left, right);\n        if (TypeGuard.TUnion(right))\n            return UnionRight(left, right);\n        if (TypeGuard.TNever(right))\n            return NeverRight(left, right);\n        if (TypeGuard.TUnknown(right))\n            return UnknownRight(left, right);\n        if (TypeGuard.TAny(right))\n            return AnyRight(left, right);\n        if (TypeGuard.TObject(right))\n            return ObjectRight(left, right);\n        if (TypeGuard.TRecord(right))\n            return RecordRight(left, right);\n        if (TypeGuard.TString(right))\n            return StringRight(left, right);\n        if (TypeGuard.TNumber(right))\n            return NumberRight(left, right);\n        if (TypeGuard.TInteger(right))\n            return IntegerRight(left, right);\n        if (TypeGuard.TBoolean(right))\n            return BooleanRight(left, right);\n        return TypeGuard.TLiteral(right) && right.const === left.const ? TypeExtendsResult.True : TypeExtendsResult.False;\n    }\n    // --------------------------------------------------------------------------\n    // Never\n    // --------------------------------------------------------------------------\n    function NeverRight(left, right) {\n        return TypeExtendsResult.False;\n    }\n    function Never(left, right) {\n        return TypeExtendsResult.True;\n    }\n    // --------------------------------------------------------------------------\n    // Not\n    // --------------------------------------------------------------------------\n    function UnwrapNot(schema) {\n        let [current, depth] = [schema, 0];\n        while (true) {\n            if (!TypeGuard.TNot(current))\n                break;\n            current = current.not;\n            depth += 1;\n        }\n        return depth % 2 === 0 ? current : exports.Type.Unknown();\n    }\n    function Not(left, right) {\n        // TypeScript has no concept of negated types, and attempts to correctly check the negated\n        // type at runtime would put TypeBox at odds with TypeScripts ability to statically infer\n        // the type. Instead we unwrap to either unknown or T and continue evaluating.\n        if (TypeGuard.TNot(left))\n            return Visit(UnwrapNot(left), right);\n        if (TypeGuard.TNot(right))\n            return Visit(left, UnwrapNot(right));\n        throw new Error(`TypeExtends: Invalid fallthrough for Not`);\n    }\n    // --------------------------------------------------------------------------\n    // Null\n    // --------------------------------------------------------------------------\n    function Null(left, right) {\n        if (TypeGuard.TIntersect(right))\n            return IntersectRight(left, right);\n        if (TypeGuard.TUnion(right))\n            return UnionRight(left, right);\n        if (TypeGuard.TNever(right))\n            return NeverRight(left, right);\n        if (TypeGuard.TUnknown(right))\n            return UnknownRight(left, right);\n        if (TypeGuard.TAny(right))\n            return AnyRight(left, right);\n        if (TypeGuard.TObject(right))\n            return ObjectRight(left, right);\n        if (TypeGuard.TRecord(right))\n            return RecordRight(left, right);\n        return TypeGuard.TNull(right) ? TypeExtendsResult.True : TypeExtendsResult.False;\n    }\n    // --------------------------------------------------------------------------\n    // Number\n    // --------------------------------------------------------------------------\n    function NumberRight(left, right) {\n        if (TypeGuard.TLiteral(left) && IsLiteralNumber(left))\n            return TypeExtendsResult.True;\n        return TypeGuard.TNumber(left) || TypeGuard.TInteger(left) ? TypeExtendsResult.True : TypeExtendsResult.False;\n    }\n    function Number(left, right) {\n        if (TypeGuard.TIntersect(right))\n            return IntersectRight(left, right);\n        if (TypeGuard.TUnion(right))\n            return UnionRight(left, right);\n        if (TypeGuard.TNever(right))\n            return NeverRight(left, right);\n        if (TypeGuard.TUnknown(right))\n            return UnknownRight(left, right);\n        if (TypeGuard.TAny(right))\n            return AnyRight(left, right);\n        if (TypeGuard.TObject(right))\n            return ObjectRight(left, right);\n        if (TypeGuard.TRecord(right))\n            return RecordRight(left, right);\n        return TypeGuard.TInteger(right) || TypeGuard.TNumber(right) ? TypeExtendsResult.True : TypeExtendsResult.False;\n    }\n    // --------------------------------------------------------------------------\n    // Object\n    // --------------------------------------------------------------------------\n    function IsObjectPropertyCount(schema, count) {\n        return globalThis.Object.keys(schema.properties).length === count;\n    }\n    function IsObjectStringLike(schema) {\n        return IsObjectArrayLike(schema);\n    }\n    function IsObjectSymbolLike(schema) {\n        // prettier-ignore\n        return IsObjectPropertyCount(schema, 0) || (IsObjectPropertyCount(schema, 1) && 'description' in schema.properties && TypeGuard.TUnion(schema.properties.description) && schema.properties.description.anyOf.length === 2 && ((TypeGuard.TString(schema.properties.description.anyOf[0]) &&\n            TypeGuard.TUndefined(schema.properties.description.anyOf[1])) || (TypeGuard.TString(schema.properties.description.anyOf[1]) &&\n            TypeGuard.TUndefined(schema.properties.description.anyOf[0]))));\n    }\n    function IsObjectNumberLike(schema) {\n        return IsObjectPropertyCount(schema, 0);\n    }\n    function IsObjectBooleanLike(schema) {\n        return IsObjectPropertyCount(schema, 0);\n    }\n    function IsObjectBigIntLike(schema) {\n        return IsObjectPropertyCount(schema, 0);\n    }\n    function IsObjectDateLike(schema) {\n        return IsObjectPropertyCount(schema, 0);\n    }\n    function IsObjectUint8ArrayLike(schema) {\n        return IsObjectArrayLike(schema);\n    }\n    function IsObjectFunctionLike(schema) {\n        const length = exports.Type.Number();\n        return IsObjectPropertyCount(schema, 0) || (IsObjectPropertyCount(schema, 1) && 'length' in schema.properties && IntoBooleanResult(Visit(schema.properties['length'], length)) === TypeExtendsResult.True);\n    }\n    function IsObjectConstructorLike(schema) {\n        return IsObjectPropertyCount(schema, 0);\n    }\n    function IsObjectArrayLike(schema) {\n        const length = exports.Type.Number();\n        return IsObjectPropertyCount(schema, 0) || (IsObjectPropertyCount(schema, 1) && 'length' in schema.properties && IntoBooleanResult(Visit(schema.properties['length'], length)) === TypeExtendsResult.True);\n    }\n    function IsObjectPromiseLike(schema) {\n        const then = exports.Type.Function([exports.Type.Any()], exports.Type.Any());\n        return IsObjectPropertyCount(schema, 0) || (IsObjectPropertyCount(schema, 1) && 'then' in schema.properties && IntoBooleanResult(Visit(schema.properties['then'], then)) === TypeExtendsResult.True);\n    }\n    // --------------------------------------------------------------------------\n    // Property\n    // --------------------------------------------------------------------------\n    function Property(left, right) {\n        if (Visit(left, right) === TypeExtendsResult.False)\n            return TypeExtendsResult.False;\n        if (TypeGuard.TOptional(left) && !TypeGuard.TOptional(right))\n            return TypeExtendsResult.False;\n        return TypeExtendsResult.True;\n    }\n    function ObjectRight(left, right) {\n        if (TypeGuard.TUnknown(left))\n            return TypeExtendsResult.False;\n        if (TypeGuard.TAny(left))\n            return TypeExtendsResult.Union;\n        if (TypeGuard.TNever(left))\n            return TypeExtendsResult.True;\n        if (TypeGuard.TLiteral(left) && IsLiteralString(left) && IsObjectStringLike(right))\n            return TypeExtendsResult.True;\n        if (TypeGuard.TLiteral(left) && IsLiteralNumber(left) && IsObjectNumberLike(right))\n            return TypeExtendsResult.True;\n        if (TypeGuard.TLiteral(left) && IsLiteralBoolean(left) && IsObjectBooleanLike(right))\n            return TypeExtendsResult.True;\n        if (TypeGuard.TSymbol(left) && IsObjectSymbolLike(right))\n            return TypeExtendsResult.True;\n        if (TypeGuard.TBigInt(left) && IsObjectBigIntLike(right))\n            return TypeExtendsResult.True;\n        if (TypeGuard.TString(left) && IsObjectStringLike(right))\n            return TypeExtendsResult.True;\n        if (TypeGuard.TSymbol(left) && IsObjectSymbolLike(right))\n            return TypeExtendsResult.True;\n        if (TypeGuard.TNumber(left) && IsObjectNumberLike(right))\n            return TypeExtendsResult.True;\n        if (TypeGuard.TInteger(left) && IsObjectNumberLike(right))\n            return TypeExtendsResult.True;\n        if (TypeGuard.TBoolean(left) && IsObjectBooleanLike(right))\n            return TypeExtendsResult.True;\n        if (TypeGuard.TUint8Array(left) && IsObjectUint8ArrayLike(right))\n            return TypeExtendsResult.True;\n        if (TypeGuard.TDate(left) && IsObjectDateLike(right))\n            return TypeExtendsResult.True;\n        if (TypeGuard.TConstructor(left) && IsObjectConstructorLike(right))\n            return TypeExtendsResult.True;\n        if (TypeGuard.TFunction(left) && IsObjectFunctionLike(right))\n            return TypeExtendsResult.True;\n        if (TypeGuard.TRecord(left) && TypeGuard.TString(RecordKey(left))) {\n            // When expressing a Record with literal key values, the Record is converted into a Object with\n            // the Hint assigned as `Record`. This is used to invert the extends logic.\n            return right[exports.Hint] === 'Record' ? TypeExtendsResult.True : TypeExtendsResult.False;\n        }\n        if (TypeGuard.TRecord(left) && TypeGuard.TNumber(RecordKey(left))) {\n            return IsObjectPropertyCount(right, 0) ? TypeExtendsResult.True : TypeExtendsResult.False;\n        }\n        return TypeExtendsResult.False;\n    }\n    function Object(left, right) {\n        if (TypeGuard.TIntersect(right))\n            return IntersectRight(left, right);\n        if (TypeGuard.TUnion(right))\n            return UnionRight(left, right);\n        if (TypeGuard.TUnknown(right))\n            return UnknownRight(left, right);\n        if (TypeGuard.TAny(right))\n            return AnyRight(left, right);\n        if (TypeGuard.TRecord(right))\n            return RecordRight(left, right);\n        if (!TypeGuard.TObject(right))\n            return TypeExtendsResult.False;\n        for (const key of globalThis.Object.keys(right.properties)) {\n            if (!(key in left.properties))\n                return TypeExtendsResult.False;\n            if (Property(left.properties[key], right.properties[key]) === TypeExtendsResult.False) {\n                return TypeExtendsResult.False;\n            }\n        }\n        return TypeExtendsResult.True;\n    }\n    // --------------------------------------------------------------------------\n    // Promise\n    // --------------------------------------------------------------------------\n    function Promise(left, right) {\n        if (TypeGuard.TIntersect(right))\n            return IntersectRight(left, right);\n        if (TypeGuard.TUnion(right))\n            return UnionRight(left, right);\n        if (TypeGuard.TUnknown(right))\n            return UnknownRight(left, right);\n        if (TypeGuard.TAny(right))\n            return AnyRight(left, right);\n        if (TypeGuard.TObject(right) && IsObjectPromiseLike(right))\n            return TypeExtendsResult.True;\n        if (!TypeGuard.TPromise(right))\n            return TypeExtendsResult.False;\n        return IntoBooleanResult(Visit(left.item, right.item));\n    }\n    // --------------------------------------------------------------------------\n    // Record\n    // --------------------------------------------------------------------------\n    function RecordKey(schema) {\n        if (exports.PatternNumberExact in schema.patternProperties)\n            return exports.Type.Number();\n        if (exports.PatternStringExact in schema.patternProperties)\n            return exports.Type.String();\n        throw Error('TypeExtends: Cannot get record key');\n    }\n    function RecordValue(schema) {\n        if (exports.PatternNumberExact in schema.patternProperties)\n            return schema.patternProperties[exports.PatternNumberExact];\n        if (exports.PatternStringExact in schema.patternProperties)\n            return schema.patternProperties[exports.PatternStringExact];\n        throw Error('TypeExtends: Cannot get record value');\n    }\n    function RecordRight(left, right) {\n        const Key = RecordKey(right);\n        const Value = RecordValue(right);\n        if (TypeGuard.TLiteral(left) && IsLiteralString(left) && TypeGuard.TNumber(Key) && IntoBooleanResult(Visit(left, Value)) === TypeExtendsResult.True)\n            return TypeExtendsResult.True;\n        if (TypeGuard.TUint8Array(left) && TypeGuard.TNumber(Key))\n            return Visit(left, Value);\n        if (TypeGuard.TString(left) && TypeGuard.TNumber(Key))\n            return Visit(left, Value);\n        if (TypeGuard.TArray(left) && TypeGuard.TNumber(Key))\n            return Visit(left, Value);\n        if (TypeGuard.TObject(left)) {\n            for (const key of globalThis.Object.keys(left.properties)) {\n                if (Property(Value, left.properties[key]) === TypeExtendsResult.False) {\n                    return TypeExtendsResult.False;\n                }\n            }\n            return TypeExtendsResult.True;\n        }\n        return TypeExtendsResult.False;\n    }\n    function Record(left, right) {\n        const Value = RecordValue(left);\n        if (TypeGuard.TIntersect(right))\n            return IntersectRight(left, right);\n        if (TypeGuard.TUnion(right))\n            return UnionRight(left, right);\n        if (TypeGuard.TUnknown(right))\n            return UnknownRight(left, right);\n        if (TypeGuard.TAny(right))\n            return AnyRight(left, right);\n        if (TypeGuard.TObject(right))\n            return ObjectRight(left, right);\n        if (!TypeGuard.TRecord(right))\n            return TypeExtendsResult.False;\n        return Visit(Value, RecordValue(right));\n    }\n    // --------------------------------------------------------------------------\n    // String\n    // --------------------------------------------------------------------------\n    function StringRight(left, right) {\n        if (TypeGuard.TLiteral(left) && typeof left.const === 'string')\n            return TypeExtendsResult.True;\n        return TypeGuard.TString(left) ? TypeExtendsResult.True : TypeExtendsResult.False;\n    }\n    function String(left, right) {\n        if (TypeGuard.TIntersect(right))\n            return IntersectRight(left, right);\n        if (TypeGuard.TUnion(right))\n            return UnionRight(left, right);\n        if (TypeGuard.TNever(right))\n            return NeverRight(left, right);\n        if (TypeGuard.TUnknown(right))\n            return UnknownRight(left, right);\n        if (TypeGuard.TAny(right))\n            return AnyRight(left, right);\n        if (TypeGuard.TObject(right))\n            return ObjectRight(left, right);\n        if (TypeGuard.TRecord(right))\n            return RecordRight(left, right);\n        return TypeGuard.TString(right) ? TypeExtendsResult.True : TypeExtendsResult.False;\n    }\n    // --------------------------------------------------------------------------\n    // Symbol\n    // --------------------------------------------------------------------------\n    function Symbol(left, right) {\n        if (TypeGuard.TIntersect(right))\n            return IntersectRight(left, right);\n        if (TypeGuard.TUnion(right))\n            return UnionRight(left, right);\n        if (TypeGuard.TNever(right))\n            return NeverRight(left, right);\n        if (TypeGuard.TUnknown(right))\n            return UnknownRight(left, right);\n        if (TypeGuard.TAny(right))\n            return AnyRight(left, right);\n        if (TypeGuard.TObject(right))\n            return ObjectRight(left, right);\n        if (TypeGuard.TRecord(right))\n            return RecordRight(left, right);\n        return TypeGuard.TSymbol(right) ? TypeExtendsResult.True : TypeExtendsResult.False;\n    }\n    // --------------------------------------------------------------------------\n    // TemplateLiteral\n    // --------------------------------------------------------------------------\n    function TemplateLiteral(left, right) {\n        // TemplateLiteral types are resolved to either unions for finite expressions or string\n        // for infinite expressions. Here we call to TemplateLiteralResolver to resolve for\n        // either type and continue evaluating.\n        if (TypeGuard.TTemplateLiteral(left))\n            return Visit(TemplateLiteralResolver.Resolve(left), right);\n        if (TypeGuard.TTemplateLiteral(right))\n            return Visit(left, TemplateLiteralResolver.Resolve(right));\n        throw new Error(`TypeExtends: Invalid fallthrough for TemplateLiteral`);\n    }\n    // --------------------------------------------------------------------------\n    // Tuple\n    // --------------------------------------------------------------------------\n    function TupleRight(left, right) {\n        if (TypeGuard.TUnknown(left))\n            return TypeExtendsResult.False;\n        if (TypeGuard.TAny(left))\n            return TypeExtendsResult.Union;\n        if (TypeGuard.TNever(left))\n            return TypeExtendsResult.True;\n        return TypeExtendsResult.False;\n    }\n    function IsArrayOfTuple(left, right) {\n        return TypeGuard.TArray(right) && left.items !== undefined && left.items.every((schema) => Visit(schema, right.items) === TypeExtendsResult.True);\n    }\n    function Tuple(left, right) {\n        if (TypeGuard.TIntersect(right))\n            return IntersectRight(left, right);\n        if (TypeGuard.TUnion(right))\n            return UnionRight(left, right);\n        if (TypeGuard.TUnknown(right))\n            return UnknownRight(left, right);\n        if (TypeGuard.TAny(right))\n            return AnyRight(left, right);\n        if (TypeGuard.TObject(right) && IsObjectArrayLike(right))\n            return TypeExtendsResult.True;\n        if (TypeGuard.TArray(right) && IsArrayOfTuple(left, right))\n            return TypeExtendsResult.True;\n        if (!TypeGuard.TTuple(right))\n            return TypeExtendsResult.False;\n        if ((left.items === undefined && right.items !== undefined) || (left.items !== undefined && right.items === undefined))\n            return TypeExtendsResult.False;\n        if (left.items === undefined && right.items === undefined)\n            return TypeExtendsResult.True;\n        return left.items.every((schema, index) => Visit(schema, right.items[index]) === TypeExtendsResult.True) ? TypeExtendsResult.True : TypeExtendsResult.False;\n    }\n    // --------------------------------------------------------------------------\n    // Uint8Array\n    // --------------------------------------------------------------------------\n    function Uint8Array(left, right) {\n        if (TypeGuard.TIntersect(right))\n            return IntersectRight(left, right);\n        if (TypeGuard.TUnion(right))\n            return UnionRight(left, right);\n        if (TypeGuard.TUnknown(right))\n            return UnknownRight(left, right);\n        if (TypeGuard.TAny(right))\n            return AnyRight(left, right);\n        if (TypeGuard.TObject(right))\n            return ObjectRight(left, right);\n        if (TypeGuard.TRecord(right))\n            return RecordRight(left, right);\n        return TypeGuard.TUint8Array(right) ? TypeExtendsResult.True : TypeExtendsResult.False;\n    }\n    // --------------------------------------------------------------------------\n    // Undefined\n    // --------------------------------------------------------------------------\n    function Undefined(left, right) {\n        if (TypeGuard.TIntersect(right))\n            return IntersectRight(left, right);\n        if (TypeGuard.TUnion(right))\n            return UnionRight(left, right);\n        if (TypeGuard.TNever(right))\n            return NeverRight(left, right);\n        if (TypeGuard.TUnknown(right))\n            return UnknownRight(left, right);\n        if (TypeGuard.TAny(right))\n            return AnyRight(left, right);\n        if (TypeGuard.TObject(right))\n            return ObjectRight(left, right);\n        if (TypeGuard.TRecord(right))\n            return RecordRight(left, right);\n        if (TypeGuard.TVoid(right))\n            return VoidRight(left, right);\n        return TypeGuard.TUndefined(right) ? TypeExtendsResult.True : TypeExtendsResult.False;\n    }\n    // --------------------------------------------------------------------------\n    // Union\n    // --------------------------------------------------------------------------\n    function UnionRight(left, right) {\n        return right.anyOf.some((schema) => Visit(left, schema) === TypeExtendsResult.True) ? TypeExtendsResult.True : TypeExtendsResult.False;\n    }\n    function Union(left, right) {\n        return left.anyOf.every((schema) => Visit(schema, right) === TypeExtendsResult.True) ? TypeExtendsResult.True : TypeExtendsResult.False;\n    }\n    // --------------------------------------------------------------------------\n    // Unknown\n    // --------------------------------------------------------------------------\n    function UnknownRight(left, right) {\n        return TypeExtendsResult.True;\n    }\n    function Unknown(left, right) {\n        if (TypeGuard.TIntersect(right))\n            return IntersectRight(left, right);\n        if (TypeGuard.TUnion(right))\n            return UnionRight(left, right);\n        if (TypeGuard.TAny(right))\n            return AnyRight(left, right);\n        if (TypeGuard.TString(right))\n            return StringRight(left, right);\n        if (TypeGuard.TNumber(right))\n            return NumberRight(left, right);\n        if (TypeGuard.TInteger(right))\n            return IntegerRight(left, right);\n        if (TypeGuard.TBoolean(right))\n            return BooleanRight(left, right);\n        if (TypeGuard.TArray(right))\n            return ArrayRight(left, right);\n        if (TypeGuard.TTuple(right))\n            return TupleRight(left, right);\n        if (TypeGuard.TObject(right))\n            return ObjectRight(left, right);\n        return TypeGuard.TUnknown(right) ? TypeExtendsResult.True : TypeExtendsResult.False;\n    }\n    // --------------------------------------------------------------------------\n    // Void\n    // --------------------------------------------------------------------------\n    function VoidRight(left, right) {\n        if (TypeGuard.TUndefined(left))\n            return TypeExtendsResult.True;\n        return TypeGuard.TUndefined(left) ? TypeExtendsResult.True : TypeExtendsResult.False;\n    }\n    function Void(left, right) {\n        if (TypeGuard.TIntersect(right))\n            return IntersectRight(left, right);\n        if (TypeGuard.TUnion(right))\n            return UnionRight(left, right);\n        if (TypeGuard.TUnknown(right))\n            return UnknownRight(left, right);\n        if (TypeGuard.TAny(right))\n            return AnyRight(left, right);\n        if (TypeGuard.TObject(right))\n            return ObjectRight(left, right);\n        return TypeGuard.TVoid(right) ? TypeExtendsResult.True : TypeExtendsResult.False;\n    }\n    function Visit(left, right) {\n        // Resolvable Types\n        if (TypeGuard.TTemplateLiteral(left) || TypeGuard.TTemplateLiteral(right))\n            return TemplateLiteral(left, right);\n        if (TypeGuard.TNot(left) || TypeGuard.TNot(right))\n            return Not(left, right);\n        // Standard Types\n        if (TypeGuard.TAny(left))\n            return Any(left, right);\n        if (TypeGuard.TArray(left))\n            return Array(left, right);\n        if (TypeGuard.TBigInt(left))\n            return BigInt(left, right);\n        if (TypeGuard.TBoolean(left))\n            return Boolean(left, right);\n        if (TypeGuard.TConstructor(left))\n            return Constructor(left, right);\n        if (TypeGuard.TDate(left))\n            return Date(left, right);\n        if (TypeGuard.TFunction(left))\n            return Function(left, right);\n        if (TypeGuard.TInteger(left))\n            return Integer(left, right);\n        if (TypeGuard.TIntersect(left))\n            return Intersect(left, right);\n        if (TypeGuard.TLiteral(left))\n            return Literal(left, right);\n        if (TypeGuard.TNever(left))\n            return Never(left, right);\n        if (TypeGuard.TNull(left))\n            return Null(left, right);\n        if (TypeGuard.TNumber(left))\n            return Number(left, right);\n        if (TypeGuard.TObject(left))\n            return Object(left, right);\n        if (TypeGuard.TRecord(left))\n            return Record(left, right);\n        if (TypeGuard.TString(left))\n            return String(left, right);\n        if (TypeGuard.TSymbol(left))\n            return Symbol(left, right);\n        if (TypeGuard.TTuple(left))\n            return Tuple(left, right);\n        if (TypeGuard.TPromise(left))\n            return Promise(left, right);\n        if (TypeGuard.TUint8Array(left))\n            return Uint8Array(left, right);\n        if (TypeGuard.TUndefined(left))\n            return Undefined(left, right);\n        if (TypeGuard.TUnion(left))\n            return Union(left, right);\n        if (TypeGuard.TUnknown(left))\n            return Unknown(left, right);\n        if (TypeGuard.TVoid(left))\n            return Void(left, right);\n        throw Error(`TypeExtends: Unknown left type operand '${left[exports.Kind]}'`);\n    }\n    function Extends(left, right) {\n        return Visit(left, right);\n    }\n    TypeExtends.Extends = Extends;\n})(TypeExtends || (exports.TypeExtends = TypeExtends = {}));\n// --------------------------------------------------------------------------\n// TypeClone\n// --------------------------------------------------------------------------\n/** Specialized Clone for Types */\nvar TypeClone;\n(function (TypeClone) {\n    function IsObject(value) {\n        return typeof value === 'object' && value !== null;\n    }\n    function IsArray(value) {\n        return globalThis.Array.isArray(value);\n    }\n    function Array(value) {\n        return value.map((value) => Visit(value));\n    }\n    function Object(value) {\n        const clonedProperties = globalThis.Object.getOwnPropertyNames(value).reduce((acc, key) => {\n            return { ...acc, [key]: Visit(value[key]) };\n        }, {});\n        const clonedSymbols = globalThis.Object.getOwnPropertySymbols(value).reduce((acc, key) => {\n            return { ...acc, [key]: Visit(value[key]) };\n        }, {});\n        return { ...clonedProperties, ...clonedSymbols };\n    }\n    function Visit(value) {\n        if (IsArray(value))\n            return Array(value);\n        if (IsObject(value))\n            return Object(value);\n        return value;\n    }\n    /** Clones a type. */\n    function Clone(schema, options) {\n        return { ...Visit(schema), ...options };\n    }\n    TypeClone.Clone = Clone;\n})(TypeClone || (exports.TypeClone = TypeClone = {}));\n// --------------------------------------------------------------------------\n// IndexedAccessor\n// --------------------------------------------------------------------------\nvar IndexedAccessor;\n(function (IndexedAccessor) {\n    function OptionalUnwrap(schema) {\n        return schema.map((schema) => {\n            const { [exports.Modifier]: _, ...clone } = TypeClone.Clone(schema, {});\n            return clone;\n        });\n    }\n    function IsIntersectOptional(schema) {\n        return schema.every((schema) => TypeGuard.TOptional(schema));\n    }\n    function IsUnionOptional(schema) {\n        return schema.some((schema) => TypeGuard.TOptional(schema));\n    }\n    function ResolveIntersect(schema) {\n        const optional = IsIntersectOptional(schema.allOf);\n        return optional ? exports.Type.Optional(exports.Type.Intersect(OptionalUnwrap(schema.allOf))) : schema;\n    }\n    function ResolveUnion(schema) {\n        const optional = IsUnionOptional(schema.anyOf);\n        return optional ? exports.Type.Optional(exports.Type.Union(OptionalUnwrap(schema.anyOf))) : schema;\n    }\n    function ResolveOptional(schema) {\n        if (schema[exports.Kind] === 'Intersect')\n            return ResolveIntersect(schema);\n        if (schema[exports.Kind] === 'Union')\n            return ResolveUnion(schema);\n        return schema;\n    }\n    function Intersect(schema, key) {\n        const resolved = schema.allOf.reduce((acc, schema) => {\n            const indexed = Visit(schema, key);\n            return indexed[exports.Kind] === 'Never' ? acc : [...acc, indexed];\n        }, []);\n        return ResolveOptional(exports.Type.Intersect(resolved));\n    }\n    function Union(schema, key) {\n        const resolved = schema.anyOf.map((schema) => Visit(schema, key));\n        return ResolveOptional(exports.Type.Union(resolved));\n    }\n    function Object(schema, key) {\n        const property = schema.properties[key];\n        return property === undefined ? exports.Type.Never() : exports.Type.Union([property]);\n    }\n    function Tuple(schema, key) {\n        const items = schema.items;\n        if (items === undefined)\n            return exports.Type.Never();\n        const element = items[key]; //\n        if (element === undefined)\n            return exports.Type.Never();\n        return element;\n    }\n    function Visit(schema, key) {\n        if (schema[exports.Kind] === 'Intersect')\n            return Intersect(schema, key);\n        if (schema[exports.Kind] === 'Union')\n            return Union(schema, key);\n        if (schema[exports.Kind] === 'Object')\n            return Object(schema, key);\n        if (schema[exports.Kind] === 'Tuple')\n            return Tuple(schema, key);\n        return exports.Type.Never();\n    }\n    function Resolve(schema, keys, options = {}) {\n        const resolved = keys.map((key) => Visit(schema, key.toString()));\n        return ResolveOptional(exports.Type.Union(resolved, options));\n    }\n    IndexedAccessor.Resolve = Resolve;\n})(IndexedAccessor || (exports.IndexedAccessor = IndexedAccessor = {}));\n// --------------------------------------------------------------------------\n// ObjectMap\n// --------------------------------------------------------------------------\nvar ObjectMap;\n(function (ObjectMap) {\n    function Intersect(schema, callback) {\n        // prettier-ignore\n        return exports.Type.Intersect(schema.allOf.map((inner) => Visit(inner, callback)), { ...schema });\n    }\n    function Union(schema, callback) {\n        // prettier-ignore\n        return exports.Type.Union(schema.anyOf.map((inner) => Visit(inner, callback)), { ...schema });\n    }\n    function Object(schema, callback) {\n        return callback(schema);\n    }\n    function Visit(schema, callback) {\n        // There are cases where users need to map objects with unregistered kinds. Using a TypeGuard here would\n        // prevent sub schema mapping as unregistered kinds will not pass TSchema checks. This is notable in the\n        // case of TObject where unregistered property kinds cause the TObject check to fail. As mapping is only\n        // used for composition, we use explicit checks instead.\n        if (schema[exports.Kind] === 'Intersect')\n            return Intersect(schema, callback);\n        if (schema[exports.Kind] === 'Union')\n            return Union(schema, callback);\n        if (schema[exports.Kind] === 'Object')\n            return Object(schema, callback);\n        return schema;\n    }\n    function Map(schema, callback, options) {\n        return { ...Visit(TypeClone.Clone(schema, {}), callback), ...options };\n    }\n    ObjectMap.Map = Map;\n})(ObjectMap || (exports.ObjectMap = ObjectMap = {}));\nvar KeyResolver;\n(function (KeyResolver) {\n    function UnwrapPattern(key) {\n        return key[0] === '^' && key[key.length - 1] === '$' ? key.slice(1, key.length - 1) : key;\n    }\n    function Intersect(schema, options) {\n        return schema.allOf.reduce((acc, schema) => [...acc, ...Visit(schema, options)], []);\n    }\n    function Union(schema, options) {\n        const sets = schema.anyOf.map((inner) => Visit(inner, options));\n        return [...sets.reduce((set, outer) => outer.map((key) => (sets.every((inner) => inner.includes(key)) ? set.add(key) : set))[0], new Set())];\n    }\n    function Object(schema, options) {\n        return globalThis.Object.keys(schema.properties);\n    }\n    function Record(schema, options) {\n        return options.includePatterns ? globalThis.Object.keys(schema.patternProperties) : [];\n    }\n    function Visit(schema, options) {\n        if (TypeGuard.TIntersect(schema))\n            return Intersect(schema, options);\n        if (TypeGuard.TUnion(schema))\n            return Union(schema, options);\n        if (TypeGuard.TObject(schema))\n            return Object(schema, options);\n        if (TypeGuard.TRecord(schema))\n            return Record(schema, options);\n        return [];\n    }\n    /** Resolves an array of keys in this schema */\n    function ResolveKeys(schema, options) {\n        return [...new Set(Visit(schema, options))];\n    }\n    KeyResolver.ResolveKeys = ResolveKeys;\n    /** Resolves a regular expression pattern matching all keys in this schema */\n    function ResolvePattern(schema) {\n        const keys = ResolveKeys(schema, { includePatterns: true });\n        const pattern = keys.map((key) => `(${UnwrapPattern(key)})`);\n        return `^(${pattern.join('|')})$`;\n    }\n    KeyResolver.ResolvePattern = ResolvePattern;\n})(KeyResolver || (exports.KeyResolver = KeyResolver = {}));\n// --------------------------------------------------------------------------\n// KeyArrayResolver\n// --------------------------------------------------------------------------\nvar KeyArrayResolver;\n(function (KeyArrayResolver) {\n    /** Resolves an array of string[] keys from the given schema or array type. */\n    function Resolve(schema) {\n        if (globalThis.Array.isArray(schema))\n            return schema;\n        if (TypeGuard.TUnionLiteral(schema))\n            return schema.anyOf.map((schema) => schema.const.toString());\n        if (TypeGuard.TLiteral(schema))\n            return [schema.const];\n        if (TypeGuard.TTemplateLiteral(schema)) {\n            const expression = TemplateLiteralParser.ParseExact(schema.pattern);\n            if (!TemplateLiteralFinite.Check(expression))\n                throw Error('KeyArrayResolver: Cannot resolve keys from infinite template expression');\n            return [...TemplateLiteralGenerator.Generate(expression)];\n        }\n        return [];\n    }\n    KeyArrayResolver.Resolve = Resolve;\n})(KeyArrayResolver || (exports.KeyArrayResolver = KeyArrayResolver = {}));\n// --------------------------------------------------------------------------\n// UnionResolver\n// --------------------------------------------------------------------------\nvar UnionResolver;\n(function (UnionResolver) {\n    function* Union(union) {\n        for (const schema of union.anyOf) {\n            if (schema[exports.Kind] === 'Union') {\n                yield* Union(schema);\n            }\n            else {\n                yield schema;\n            }\n        }\n    }\n    /** Returns a resolved union with interior unions flattened */\n    function Resolve(union) {\n        return exports.Type.Union([...Union(union)], { ...union });\n    }\n    UnionResolver.Resolve = Resolve;\n})(UnionResolver || (exports.UnionResolver = UnionResolver = {}));\n// --------------------------------------------------------------------------\n// TemplateLiteralPattern\n// --------------------------------------------------------------------------\nvar TemplateLiteralPattern;\n(function (TemplateLiteralPattern) {\n    function Escape(value) {\n        return value.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n    }\n    function Visit(schema, acc) {\n        if (TypeGuard.TTemplateLiteral(schema)) {\n            const pattern = schema.pattern.slice(1, schema.pattern.length - 1);\n            return pattern;\n        }\n        else if (TypeGuard.TUnion(schema)) {\n            const tokens = schema.anyOf.map((schema) => Visit(schema, acc)).join('|');\n            return `(${tokens})`;\n        }\n        else if (TypeGuard.TNumber(schema)) {\n            return `${acc}${exports.PatternNumber}`;\n        }\n        else if (TypeGuard.TInteger(schema)) {\n            return `${acc}${exports.PatternNumber}`;\n        }\n        else if (TypeGuard.TBigInt(schema)) {\n            return `${acc}${exports.PatternNumber}`;\n        }\n        else if (TypeGuard.TString(schema)) {\n            return `${acc}${exports.PatternString}`;\n        }\n        else if (TypeGuard.TLiteral(schema)) {\n            return `${acc}${Escape(schema.const.toString())}`;\n        }\n        else if (TypeGuard.TBoolean(schema)) {\n            return `${acc}${exports.PatternBoolean}`;\n        }\n        else if (TypeGuard.TNever(schema)) {\n            throw Error('TemplateLiteralPattern: TemplateLiteral cannot operate on types of TNever');\n        }\n        else {\n            throw Error(`TemplateLiteralPattern: Unexpected Kind '${schema[exports.Kind]}'`);\n        }\n    }\n    function Create(kinds) {\n        return `^${kinds.map((schema) => Visit(schema, '')).join('')}\\$`;\n    }\n    TemplateLiteralPattern.Create = Create;\n})(TemplateLiteralPattern || (exports.TemplateLiteralPattern = TemplateLiteralPattern = {}));\n// --------------------------------------------------------------------------------------\n// TemplateLiteralResolver\n// --------------------------------------------------------------------------------------\nvar TemplateLiteralResolver;\n(function (TemplateLiteralResolver) {\n    /** Resolves a template literal as a TUnion */\n    function Resolve(template) {\n        const expression = TemplateLiteralParser.ParseExact(template.pattern);\n        if (!TemplateLiteralFinite.Check(expression))\n            return exports.Type.String();\n        const literals = [...TemplateLiteralGenerator.Generate(expression)].map((value) => exports.Type.Literal(value));\n        return exports.Type.Union(literals);\n    }\n    TemplateLiteralResolver.Resolve = Resolve;\n})(TemplateLiteralResolver || (exports.TemplateLiteralResolver = TemplateLiteralResolver = {}));\n// --------------------------------------------------------------------------------------\n// TemplateLiteralParser\n// --------------------------------------------------------------------------------------\nclass TemplateLiteralParserError extends Error {\n    constructor(message) {\n        super(message);\n    }\n}\nexports.TemplateLiteralParserError = TemplateLiteralParserError;\nvar TemplateLiteralParser;\n(function (TemplateLiteralParser) {\n    function IsNonEscaped(pattern, index, char) {\n        return pattern[index] === char && pattern.charCodeAt(index - 1) !== 92;\n    }\n    function IsOpenParen(pattern, index) {\n        return IsNonEscaped(pattern, index, '(');\n    }\n    function IsCloseParen(pattern, index) {\n        return IsNonEscaped(pattern, index, ')');\n    }\n    function IsSeparator(pattern, index) {\n        return IsNonEscaped(pattern, index, '|');\n    }\n    function IsGroup(pattern) {\n        if (!(IsOpenParen(pattern, 0) && IsCloseParen(pattern, pattern.length - 1)))\n            return false;\n        let count = 0;\n        for (let index = 0; index < pattern.length; index++) {\n            if (IsOpenParen(pattern, index))\n                count += 1;\n            if (IsCloseParen(pattern, index))\n                count -= 1;\n            if (count === 0 && index !== pattern.length - 1)\n                return false;\n        }\n        return true;\n    }\n    function InGroup(pattern) {\n        return pattern.slice(1, pattern.length - 1);\n    }\n    function IsPrecedenceOr(pattern) {\n        let count = 0;\n        for (let index = 0; index < pattern.length; index++) {\n            if (IsOpenParen(pattern, index))\n                count += 1;\n            if (IsCloseParen(pattern, index))\n                count -= 1;\n            if (IsSeparator(pattern, index) && count === 0)\n                return true;\n        }\n        return false;\n    }\n    function IsPrecedenceAnd(pattern) {\n        for (let index = 0; index < pattern.length; index++) {\n            if (IsOpenParen(pattern, index))\n                return true;\n        }\n        return false;\n    }\n    function Or(pattern) {\n        let [count, start] = [0, 0];\n        const expressions = [];\n        for (let index = 0; index < pattern.length; index++) {\n            if (IsOpenParen(pattern, index))\n                count += 1;\n            if (IsCloseParen(pattern, index))\n                count -= 1;\n            if (IsSeparator(pattern, index) && count === 0) {\n                const range = pattern.slice(start, index);\n                if (range.length > 0)\n                    expressions.push(Parse(range));\n                start = index + 1;\n            }\n        }\n        const range = pattern.slice(start);\n        if (range.length > 0)\n            expressions.push(Parse(range));\n        if (expressions.length === 0)\n            return { type: 'const', const: '' };\n        if (expressions.length === 1)\n            return expressions[0];\n        return { type: 'or', expr: expressions };\n    }\n    function And(pattern) {\n        function Group(value, index) {\n            if (!IsOpenParen(value, index))\n                throw new TemplateLiteralParserError(`TemplateLiteralParser: Index must point to open parens`);\n            let count = 0;\n            for (let scan = index; scan < value.length; scan++) {\n                if (IsOpenParen(value, scan))\n                    count += 1;\n                if (IsCloseParen(value, scan))\n                    count -= 1;\n                if (count === 0)\n                    return [index, scan];\n            }\n            throw new TemplateLiteralParserError(`TemplateLiteralParser: Unclosed group parens in expression`);\n        }\n        function Range(pattern, index) {\n            for (let scan = index; scan < pattern.length; scan++) {\n                if (IsOpenParen(pattern, scan))\n                    return [index, scan];\n            }\n            return [index, pattern.length];\n        }\n        const expressions = [];\n        for (let index = 0; index < pattern.length; index++) {\n            if (IsOpenParen(pattern, index)) {\n                const [start, end] = Group(pattern, index);\n                const range = pattern.slice(start, end + 1);\n                expressions.push(Parse(range));\n                index = end;\n            }\n            else {\n                const [start, end] = Range(pattern, index);\n                const range = pattern.slice(start, end);\n                if (range.length > 0)\n                    expressions.push(Parse(range));\n                index = end - 1;\n            }\n        }\n        if (expressions.length === 0)\n            return { type: 'const', const: '' };\n        if (expressions.length === 1)\n            return expressions[0];\n        return { type: 'and', expr: expressions };\n    }\n    /** Parses a pattern and returns an expression tree */\n    function Parse(pattern) {\n        if (IsGroup(pattern))\n            return Parse(InGroup(pattern));\n        if (IsPrecedenceOr(pattern))\n            return Or(pattern);\n        if (IsPrecedenceAnd(pattern))\n            return And(pattern);\n        return { type: 'const', const: pattern };\n    }\n    TemplateLiteralParser.Parse = Parse;\n    /** Parses a pattern and strips forward and trailing ^ and $ */\n    function ParseExact(pattern) {\n        return Parse(pattern.slice(1, pattern.length - 1));\n    }\n    TemplateLiteralParser.ParseExact = ParseExact;\n})(TemplateLiteralParser || (exports.TemplateLiteralParser = TemplateLiteralParser = {}));\n// --------------------------------------------------------------------------------------\n// TemplateLiteralFinite\n// --------------------------------------------------------------------------------------\nvar TemplateLiteralFinite;\n(function (TemplateLiteralFinite) {\n    function IsNumber(expression) {\n        // prettier-ignore\n        return (expression.type === 'or' &&\n            expression.expr.length === 2 &&\n            expression.expr[0].type === 'const' &&\n            expression.expr[0].const === '0' &&\n            expression.expr[1].type === 'const' &&\n            expression.expr[1].const === '[1-9][0-9]*');\n    }\n    function IsBoolean(expression) {\n        // prettier-ignore\n        return (expression.type === 'or' &&\n            expression.expr.length === 2 &&\n            expression.expr[0].type === 'const' &&\n            expression.expr[0].const === 'true' &&\n            expression.expr[1].type === 'const' &&\n            expression.expr[1].const === 'false');\n    }\n    function IsString(expression) {\n        return expression.type === 'const' && expression.const === '.*';\n    }\n    function Check(expression) {\n        if (IsBoolean(expression))\n            return true;\n        if (IsNumber(expression) || IsString(expression))\n            return false;\n        if (expression.type === 'and')\n            return expression.expr.every((expr) => Check(expr));\n        if (expression.type === 'or')\n            return expression.expr.every((expr) => Check(expr));\n        if (expression.type === 'const')\n            return true;\n        throw Error(`TemplateLiteralFinite: Unknown expression type`);\n    }\n    TemplateLiteralFinite.Check = Check;\n})(TemplateLiteralFinite || (exports.TemplateLiteralFinite = TemplateLiteralFinite = {}));\n// --------------------------------------------------------------------------------------\n// TemplateLiteralGenerator\n// --------------------------------------------------------------------------------------\nvar TemplateLiteralGenerator;\n(function (TemplateLiteralGenerator) {\n    function* Reduce(buffer) {\n        if (buffer.length === 1)\n            return yield* buffer[0];\n        for (const left of buffer[0]) {\n            for (const right of Reduce(buffer.slice(1))) {\n                yield `${left}${right}`;\n            }\n        }\n    }\n    function* And(expression) {\n        return yield* Reduce(expression.expr.map((expr) => [...Generate(expr)]));\n    }\n    function* Or(expression) {\n        for (const expr of expression.expr)\n            yield* Generate(expr);\n    }\n    function* Const(expression) {\n        return yield expression.const;\n    }\n    function* Generate(expression) {\n        if (expression.type === 'and')\n            return yield* And(expression);\n        if (expression.type === 'or')\n            return yield* Or(expression);\n        if (expression.type === 'const')\n            return yield* Const(expression);\n        throw Error('TemplateLiteralGenerator: Unknown expression');\n    }\n    TemplateLiteralGenerator.Generate = Generate;\n})(TemplateLiteralGenerator || (exports.TemplateLiteralGenerator = TemplateLiteralGenerator = {}));\n// ---------------------------------------------------------------------\n// TemplateLiteralDslParser\n// ---------------------------------------------------------------------\nvar TemplateLiteralDslParser;\n(function (TemplateLiteralDslParser) {\n    function* ParseUnion(template) {\n        const trim = template.trim().replace(/\"|'/g, '');\n        if (trim === 'boolean')\n            return yield exports.Type.Boolean();\n        if (trim === 'number')\n            return yield exports.Type.Number();\n        if (trim === 'bigint')\n            return yield exports.Type.BigInt();\n        if (trim === 'string')\n            return yield exports.Type.String();\n        const literals = trim.split('|').map((literal) => exports.Type.Literal(literal.trim()));\n        return yield literals.length === 0 ? exports.Type.Never() : literals.length === 1 ? literals[0] : exports.Type.Union(literals);\n    }\n    function* ParseTerminal(template) {\n        if (template[1] !== '{') {\n            const L = exports.Type.Literal('$');\n            const R = ParseLiteral(template.slice(1));\n            return yield* [L, ...R];\n        }\n        for (let i = 2; i < template.length; i++) {\n            if (template[i] === '}') {\n                const L = ParseUnion(template.slice(2, i));\n                const R = ParseLiteral(template.slice(i + 1));\n                return yield* [...L, ...R];\n            }\n        }\n        yield exports.Type.Literal(template);\n    }\n    function* ParseLiteral(template) {\n        for (let i = 0; i < template.length; i++) {\n            if (template[i] === '$') {\n                const L = exports.Type.Literal(template.slice(0, i));\n                const R = ParseTerminal(template.slice(i));\n                return yield* [L, ...R];\n            }\n        }\n        yield exports.Type.Literal(template);\n    }\n    function Parse(template_dsl) {\n        return [...ParseLiteral(template_dsl)];\n    }\n    TemplateLiteralDslParser.Parse = Parse;\n})(TemplateLiteralDslParser || (exports.TemplateLiteralDslParser = TemplateLiteralDslParser = {}));\n// --------------------------------------------------------------------------\n// TypeOrdinal: Used for auto $id generation\n// --------------------------------------------------------------------------\nlet TypeOrdinal = 0;\n// --------------------------------------------------------------------------\n// TypeBuilder\n// --------------------------------------------------------------------------\nclass TypeBuilder {\n    /** `[Utility]` Creates a schema without `static` and `params` types */\n    Create(schema) {\n        return schema;\n    }\n    /** `[Standard]` Omits compositing symbols from this schema */\n    Strict(schema) {\n        return JSON.parse(JSON.stringify(schema));\n    }\n}\nexports.TypeBuilder = TypeBuilder;\n// --------------------------------------------------------------------------\n// StandardTypeBuilder\n// --------------------------------------------------------------------------\nclass StandardTypeBuilder extends TypeBuilder {\n    // ------------------------------------------------------------------------\n    // Modifiers\n    // ------------------------------------------------------------------------\n    /** `[Modifier]` Creates a Optional property */\n    Optional(schema) {\n        return { [exports.Modifier]: 'Optional', ...TypeClone.Clone(schema, {}) };\n    }\n    /** `[Modifier]` Creates a ReadonlyOptional property */\n    ReadonlyOptional(schema) {\n        return { [exports.Modifier]: 'ReadonlyOptional', ...TypeClone.Clone(schema, {}) };\n    }\n    /** `[Modifier]` Creates a Readonly object or property */\n    Readonly(schema) {\n        return { [exports.Modifier]: 'Readonly', ...schema };\n    }\n    // ------------------------------------------------------------------------\n    // Types\n    // ------------------------------------------------------------------------\n    /** `[Standard]` Creates an Any type */\n    Any(options = {}) {\n        return this.Create({ ...options, [exports.Kind]: 'Any' });\n    }\n    /** `[Standard]` Creates an Array type */\n    Array(items, options = {}) {\n        return this.Create({ ...options, [exports.Kind]: 'Array', type: 'array', items: TypeClone.Clone(items, {}) });\n    }\n    /** `[Standard]` Creates a Boolean type */\n    Boolean(options = {}) {\n        return this.Create({ ...options, [exports.Kind]: 'Boolean', type: 'boolean' });\n    }\n    /** `[Standard]` Creates a Composite object type. */\n    Composite(objects, options) {\n        const intersect = exports.Type.Intersect(objects, {});\n        const keys = KeyResolver.ResolveKeys(intersect, { includePatterns: false });\n        const properties = keys.reduce((acc, key) => ({ ...acc, [key]: exports.Type.Index(intersect, [key]) }), {});\n        return exports.Type.Object(properties, options);\n    }\n    /** `[Standard]` Creates a Enum type */\n    Enum(item, options = {}) {\n        // prettier-ignore\n        const values = globalThis.Object.keys(item).filter((key) => isNaN(key)).map((key) => item[key]);\n        const anyOf = values.map((value) => (typeof value === 'string' ? { [exports.Kind]: 'Literal', type: 'string', const: value } : { [exports.Kind]: 'Literal', type: 'number', const: value }));\n        return this.Create({ ...options, [exports.Kind]: 'Union', anyOf });\n    }\n    /** `[Standard]` A conditional type expression that will return the true type if the left type extends the right */\n    Extends(left, right, trueType, falseType, options = {}) {\n        switch (TypeExtends.Extends(left, right)) {\n            case TypeExtendsResult.Union:\n                return this.Union([TypeClone.Clone(trueType, options), TypeClone.Clone(falseType, options)]);\n            case TypeExtendsResult.True:\n                return TypeClone.Clone(trueType, options);\n            case TypeExtendsResult.False:\n                return TypeClone.Clone(falseType, options);\n        }\n    }\n    /** `[Standard]` Excludes from the left type any type that is not assignable to the right */\n    Exclude(left, right, options = {}) {\n        if (TypeGuard.TTemplateLiteral(left))\n            return this.Exclude(TemplateLiteralResolver.Resolve(left), right, options);\n        if (TypeGuard.TTemplateLiteral(right))\n            return this.Exclude(left, TemplateLiteralResolver.Resolve(right), options);\n        if (TypeGuard.TUnion(left)) {\n            const narrowed = left.anyOf.filter((inner) => TypeExtends.Extends(inner, right) === TypeExtendsResult.False);\n            return (narrowed.length === 1 ? TypeClone.Clone(narrowed[0], options) : this.Union(narrowed, options));\n        }\n        else {\n            return (TypeExtends.Extends(left, right) !== TypeExtendsResult.False ? this.Never(options) : TypeClone.Clone(left, options));\n        }\n    }\n    /** `[Standard]` Extracts from the left type any type that is assignable to the right */\n    Extract(left, right, options = {}) {\n        if (TypeGuard.TTemplateLiteral(left))\n            return this.Extract(TemplateLiteralResolver.Resolve(left), right, options);\n        if (TypeGuard.TTemplateLiteral(right))\n            return this.Extract(left, TemplateLiteralResolver.Resolve(right), options);\n        if (TypeGuard.TUnion(left)) {\n            const narrowed = left.anyOf.filter((inner) => TypeExtends.Extends(inner, right) !== TypeExtendsResult.False);\n            return (narrowed.length === 1 ? TypeClone.Clone(narrowed[0], options) : this.Union(narrowed, options));\n        }\n        else {\n            return (TypeExtends.Extends(left, right) !== TypeExtendsResult.False ? TypeClone.Clone(left, options) : this.Never(options));\n        }\n    }\n    /** `[Standard]` Returns indexed property types for the given keys */\n    Index(schema, unresolved, options = {}) {\n        if (TypeGuard.TArray(schema) && TypeGuard.TNumber(unresolved)) {\n            return TypeClone.Clone(schema.items, options);\n        }\n        else if (TypeGuard.TTuple(schema) && TypeGuard.TNumber(unresolved)) {\n            const items = schema.items === undefined ? [] : schema.items;\n            const cloned = items.map((schema) => TypeClone.Clone(schema, {}));\n            return this.Union(cloned, options);\n        }\n        else {\n            const keys = KeyArrayResolver.Resolve(unresolved);\n            const clone = TypeClone.Clone(schema, {});\n            return IndexedAccessor.Resolve(clone, keys, options);\n        }\n    }\n    /** `[Standard]` Creates an Integer type */\n    Integer(options = {}) {\n        return this.Create({ ...options, [exports.Kind]: 'Integer', type: 'integer' });\n    }\n    Intersect(allOf, options = {}) {\n        if (allOf.length === 0)\n            return exports.Type.Never();\n        if (allOf.length === 1)\n            return TypeClone.Clone(allOf[0], options);\n        const objects = allOf.every((schema) => TypeGuard.TObject(schema));\n        const cloned = allOf.map((schema) => TypeClone.Clone(schema, {}));\n        const clonedUnevaluatedProperties = TypeGuard.TSchema(options.unevaluatedProperties) ? { unevaluatedProperties: TypeClone.Clone(options.unevaluatedProperties, {}) } : {};\n        if (options.unevaluatedProperties === false || TypeGuard.TSchema(options.unevaluatedProperties) || objects) {\n            return this.Create({ ...options, ...clonedUnevaluatedProperties, [exports.Kind]: 'Intersect', type: 'object', allOf: cloned });\n        }\n        else {\n            return this.Create({ ...options, ...clonedUnevaluatedProperties, [exports.Kind]: 'Intersect', allOf: cloned });\n        }\n    }\n    /** `[Standard]` Creates a KeyOf type */\n    KeyOf(schema, options = {}) {\n        if (TypeGuard.TRecord(schema)) {\n            const pattern = Object.getOwnPropertyNames(schema.patternProperties)[0];\n            if (pattern === exports.PatternNumberExact)\n                return this.Number(options);\n            if (pattern === exports.PatternStringExact)\n                return this.String(options);\n            throw Error('StandardTypeBuilder: Unable to resolve key type from Record key pattern');\n        }\n        else if (TypeGuard.TTuple(schema)) {\n            const items = schema.items === undefined ? [] : schema.items;\n            const literals = items.map((_, index) => exports.Type.Literal(index));\n            return this.Union(literals, options);\n        }\n        else if (TypeGuard.TArray(schema)) {\n            return this.Number(options);\n        }\n        else {\n            const keys = KeyResolver.ResolveKeys(schema, { includePatterns: false });\n            if (keys.length === 0)\n                return this.Never(options);\n            const literals = keys.map((key) => this.Literal(key));\n            return this.Union(literals, options);\n        }\n    }\n    /** `[Standard]` Creates a Literal type */\n    Literal(value, options = {}) {\n        return this.Create({ ...options, [exports.Kind]: 'Literal', const: value, type: typeof value });\n    }\n    /** `[Standard]` Creates a Never type */\n    Never(options = {}) {\n        return this.Create({ ...options, [exports.Kind]: 'Never', not: {} });\n    }\n    /** `[Standard]` Creates a Not type */\n    Not(not, options) {\n        return this.Create({ ...options, [exports.Kind]: 'Not', not });\n    }\n    /** `[Standard]` Creates a Null type */\n    Null(options = {}) {\n        return this.Create({ ...options, [exports.Kind]: 'Null', type: 'null' });\n    }\n    /** `[Standard]` Creates a Number type */\n    Number(options = {}) {\n        return this.Create({ ...options, [exports.Kind]: 'Number', type: 'number' });\n    }\n    /** `[Standard]` Creates an Object type */\n    Object(properties, options = {}) {\n        const propertyKeys = globalThis.Object.getOwnPropertyNames(properties);\n        const optionalKeys = propertyKeys.filter((key) => TypeGuard.TOptional(properties[key]) || TypeGuard.TReadonlyOptional(properties[key]));\n        const requiredKeys = propertyKeys.filter((name) => !optionalKeys.includes(name));\n        const clonedAdditionalProperties = TypeGuard.TSchema(options.additionalProperties) ? { additionalProperties: TypeClone.Clone(options.additionalProperties, {}) } : {};\n        const clonedProperties = propertyKeys.reduce((acc, key) => ({ ...acc, [key]: TypeClone.Clone(properties[key], {}) }), {});\n        if (requiredKeys.length > 0) {\n            return this.Create({ ...options, ...clonedAdditionalProperties, [exports.Kind]: 'Object', type: 'object', properties: clonedProperties, required: requiredKeys });\n        }\n        else {\n            return this.Create({ ...options, ...clonedAdditionalProperties, [exports.Kind]: 'Object', type: 'object', properties: clonedProperties });\n        }\n    }\n    Omit(schema, unresolved, options = {}) {\n        const keys = KeyArrayResolver.Resolve(unresolved);\n        // prettier-ignore\n        return ObjectMap.Map(TypeClone.Clone(schema, {}), (schema) => {\n            if (schema.required) {\n                schema.required = schema.required.filter((key) => !keys.includes(key));\n                if (schema.required.length === 0)\n                    delete schema.required;\n            }\n            for (const key of globalThis.Object.keys(schema.properties)) {\n                if (keys.includes(key))\n                    delete schema.properties[key];\n            }\n            return this.Create(schema);\n        }, options);\n    }\n    /** `[Standard]` Creates a mapped type where all properties are Optional */\n    Partial(schema, options = {}) {\n        function Apply(schema) {\n            // prettier-ignore\n            switch (schema[exports.Modifier]) {\n                case 'ReadonlyOptional':\n                    schema[exports.Modifier] = 'ReadonlyOptional';\n                    break;\n                case 'Readonly':\n                    schema[exports.Modifier] = 'ReadonlyOptional';\n                    break;\n                case 'Optional':\n                    schema[exports.Modifier] = 'Optional';\n                    break;\n                default:\n                    schema[exports.Modifier] = 'Optional';\n                    break;\n            }\n        }\n        // prettier-ignore\n        return ObjectMap.Map(TypeClone.Clone(schema, {}), (schema) => {\n            delete schema.required;\n            globalThis.Object.keys(schema.properties).forEach(key => Apply(schema.properties[key]));\n            return schema;\n        }, options);\n    }\n    Pick(schema, unresolved, options = {}) {\n        const keys = KeyArrayResolver.Resolve(unresolved);\n        // prettier-ignore\n        return ObjectMap.Map(TypeClone.Clone(schema, {}), (schema) => {\n            if (schema.required) {\n                schema.required = schema.required.filter((key) => keys.includes(key));\n                if (schema.required.length === 0)\n                    delete schema.required;\n            }\n            for (const key of globalThis.Object.keys(schema.properties)) {\n                if (!keys.includes(key))\n                    delete schema.properties[key];\n            }\n            return this.Create(schema);\n        }, options);\n    }\n    /** `[Standard]` Creates a Record type */\n    Record(key, schema, options = {}) {\n        if (TypeGuard.TTemplateLiteral(key)) {\n            const expression = TemplateLiteralParser.ParseExact(key.pattern);\n            // prettier-ignore\n            return TemplateLiteralFinite.Check(expression)\n                ? (this.Object([...TemplateLiteralGenerator.Generate(expression)].reduce((acc, key) => ({ ...acc, [key]: TypeClone.Clone(schema, {}) }), {}), options))\n                : this.Create({ ...options, [exports.Kind]: 'Record', type: 'object', patternProperties: { [key.pattern]: TypeClone.Clone(schema, {}) } });\n        }\n        else if (TypeGuard.TUnion(key)) {\n            const union = UnionResolver.Resolve(key);\n            if (TypeGuard.TUnionLiteral(union)) {\n                const properties = union.anyOf.reduce((acc, literal) => ({ ...acc, [literal.const]: TypeClone.Clone(schema, {}) }), {});\n                return this.Object(properties, { ...options, [exports.Hint]: 'Record' });\n            }\n            else\n                throw Error('TypeBuilder: Record key of type union contains non-literal types');\n        }\n        else if (TypeGuard.TLiteral(key)) {\n            if (typeof key.const === 'string' || typeof key.const === 'number') {\n                return this.Object({ [key.const]: TypeClone.Clone(schema, {}) }, options);\n            }\n            else\n                throw Error('TypeBuilder: Record key of type literal is not of type string or number');\n        }\n        else if (TypeGuard.TInteger(key) || TypeGuard.TNumber(key)) {\n            const pattern = exports.PatternNumberExact;\n            return this.Create({ ...options, [exports.Kind]: 'Record', type: 'object', patternProperties: { [pattern]: TypeClone.Clone(schema, {}) } });\n        }\n        else if (TypeGuard.TString(key)) {\n            const pattern = key.pattern === undefined ? exports.PatternStringExact : key.pattern;\n            return this.Create({ ...options, [exports.Kind]: 'Record', type: 'object', patternProperties: { [pattern]: TypeClone.Clone(schema, {}) } });\n        }\n        else {\n            throw Error(`StandardTypeBuilder: Record key is an invalid type`);\n        }\n    }\n    /** `[Standard]` Creates a Recursive type */\n    Recursive(callback, options = {}) {\n        if (options.$id === undefined)\n            options.$id = `T${TypeOrdinal++}`;\n        const thisType = callback({ [exports.Kind]: 'This', $ref: `${options.$id}` });\n        thisType.$id = options.$id;\n        return this.Create({ ...options, [exports.Hint]: 'Recursive', ...thisType });\n    }\n    /** `[Standard]` Creates a Ref type. The referenced type must contain a $id */\n    Ref(schema, options = {}) {\n        if (schema.$id === undefined)\n            throw Error('StandardTypeBuilder.Ref: Target type must specify an $id');\n        return this.Create({ ...options, [exports.Kind]: 'Ref', $ref: schema.$id });\n    }\n    /** `[Standard]` Creates a mapped type where all properties are Required */\n    Required(schema, options = {}) {\n        function Apply(schema) {\n            // prettier-ignore\n            switch (schema[exports.Modifier]) {\n                case 'ReadonlyOptional':\n                    schema[exports.Modifier] = 'Readonly';\n                    break;\n                case 'Readonly':\n                    schema[exports.Modifier] = 'Readonly';\n                    break;\n                case 'Optional':\n                    delete schema[exports.Modifier];\n                    break;\n                default:\n                    delete schema[exports.Modifier];\n                    break;\n            }\n        }\n        // prettier-ignore\n        return ObjectMap.Map(TypeClone.Clone(schema, {}), (schema) => {\n            schema.required = globalThis.Object.keys(schema.properties);\n            globalThis.Object.keys(schema.properties).forEach(key => Apply(schema.properties[key]));\n            return schema;\n        }, options);\n    }\n    /** `[Standard]` Returns a schema array which allows types to compose with the JavaScript spread operator */\n    Rest(schema) {\n        if (TypeGuard.TTuple(schema)) {\n            if (schema.items === undefined)\n                return [];\n            return schema.items.map((schema) => TypeClone.Clone(schema, {}));\n        }\n        else {\n            return [TypeClone.Clone(schema, {})];\n        }\n    }\n    /** `[Standard]` Creates a String type */\n    String(options = {}) {\n        return this.Create({ ...options, [exports.Kind]: 'String', type: 'string' });\n    }\n    /** `[Standard]` Creates a template literal type */\n    TemplateLiteral(unresolved, options = {}) {\n        // prettier-ignore\n        const pattern = (typeof unresolved === 'string')\n            ? TemplateLiteralPattern.Create(TemplateLiteralDslParser.Parse(unresolved))\n            : TemplateLiteralPattern.Create(unresolved);\n        return this.Create({ ...options, [exports.Kind]: 'TemplateLiteral', type: 'string', pattern });\n    }\n    /** `[Standard]` Creates a Tuple type */\n    Tuple(items, options = {}) {\n        const [additionalItems, minItems, maxItems] = [false, items.length, items.length];\n        const clonedItems = items.map((item) => TypeClone.Clone(item, {}));\n        // prettier-ignore\n        const schema = (items.length > 0 ?\n            { ...options, [exports.Kind]: 'Tuple', type: 'array', items: clonedItems, additionalItems, minItems, maxItems } :\n            { ...options, [exports.Kind]: 'Tuple', type: 'array', minItems, maxItems });\n        return this.Create(schema);\n    }\n    Union(union, options = {}) {\n        if (TypeGuard.TTemplateLiteral(union)) {\n            return TemplateLiteralResolver.Resolve(union);\n        }\n        else {\n            const anyOf = union;\n            if (anyOf.length === 0)\n                return this.Never(options);\n            if (anyOf.length === 1)\n                return this.Create(TypeClone.Clone(anyOf[0], options));\n            const clonedAnyOf = anyOf.map((schema) => TypeClone.Clone(schema, {}));\n            return this.Create({ ...options, [exports.Kind]: 'Union', anyOf: clonedAnyOf });\n        }\n    }\n    /** `[Standard]` Creates an Unknown type */\n    Unknown(options = {}) {\n        return this.Create({ ...options, [exports.Kind]: 'Unknown' });\n    }\n    /** `[Standard]` Creates a Unsafe type that infers for the generic argument */\n    Unsafe(options = {}) {\n        return this.Create({ ...options, [exports.Kind]: options[exports.Kind] || 'Unsafe' });\n    }\n}\nexports.StandardTypeBuilder = StandardTypeBuilder;\n// --------------------------------------------------------------------------\n// ExtendedTypeBuilder\n// --------------------------------------------------------------------------\nclass ExtendedTypeBuilder extends StandardTypeBuilder {\n    /** `[Extended]` Creates a BigInt type */\n    BigInt(options = {}) {\n        return this.Create({ ...options, [exports.Kind]: 'BigInt', type: 'null', typeOf: 'BigInt' });\n    }\n    /** `[Extended]` Extracts the ConstructorParameters from the given Constructor type */\n    ConstructorParameters(schema, options = {}) {\n        return this.Tuple([...schema.parameters], { ...options });\n    }\n    /** `[Extended]` Creates a Constructor type */\n    Constructor(parameters, returns, options) {\n        const clonedReturns = TypeClone.Clone(returns, {});\n        const clonedParameters = parameters.map((parameter) => TypeClone.Clone(parameter, {}));\n        return this.Create({ ...options, [exports.Kind]: 'Constructor', type: 'object', instanceOf: 'Constructor', parameters: clonedParameters, returns: clonedReturns });\n    }\n    /** `[Extended]` Creates a Date type */\n    Date(options = {}) {\n        return this.Create({ ...options, [exports.Kind]: 'Date', type: 'object', instanceOf: 'Date' });\n    }\n    /** `[Extended]` Creates a Function type */\n    Function(parameters, returns, options) {\n        const clonedReturns = TypeClone.Clone(returns, {});\n        const clonedParameters = parameters.map((parameter) => TypeClone.Clone(parameter, {}));\n        return this.Create({ ...options, [exports.Kind]: 'Function', type: 'object', instanceOf: 'Function', parameters: clonedParameters, returns: clonedReturns });\n    }\n    /** `[Extended]` Extracts the InstanceType from the given Constructor */\n    InstanceType(schema, options = {}) {\n        return TypeClone.Clone(schema.returns, options);\n    }\n    /** `[Extended]` Extracts the Parameters from the given Function type */\n    Parameters(schema, options = {}) {\n        return this.Tuple(schema.parameters, { ...options });\n    }\n    /** `[Extended]` Creates a Promise type */\n    Promise(item, options = {}) {\n        return this.Create({ ...options, [exports.Kind]: 'Promise', type: 'object', instanceOf: 'Promise', item: TypeClone.Clone(item, {}) });\n    }\n    /** `[Extended]` Creates a regular expression type */\n    RegEx(regex, options = {}) {\n        return this.Create({ ...options, [exports.Kind]: 'String', type: 'string', pattern: regex.source });\n    }\n    /** `[Extended]` Extracts the ReturnType from the given Function */\n    ReturnType(schema, options = {}) {\n        return TypeClone.Clone(schema.returns, options);\n    }\n    /** `[Extended]` Creates a Symbol type */\n    Symbol(options) {\n        return this.Create({ ...options, [exports.Kind]: 'Symbol', type: 'null', typeOf: 'Symbol' });\n    }\n    /** `[Extended]` Creates a Undefined type */\n    Undefined(options = {}) {\n        return this.Create({ ...options, [exports.Kind]: 'Undefined', type: 'null', typeOf: 'Undefined' });\n    }\n    /** `[Extended]` Creates a Uint8Array type */\n    Uint8Array(options = {}) {\n        return this.Create({ ...options, [exports.Kind]: 'Uint8Array', type: 'object', instanceOf: 'Uint8Array' });\n    }\n    /** `[Extended]` Creates a Void type */\n    Void(options = {}) {\n        return this.Create({ ...options, [exports.Kind]: 'Void', type: 'null', typeOf: 'Void' });\n    }\n}\nexports.ExtendedTypeBuilder = ExtendedTypeBuilder;\n/** JSON Schema TypeBuilder with Static Resolution for TypeScript */\nexports.StandardType = new StandardTypeBuilder();\n/** JSON Schema TypeBuilder with Static Resolution for TypeScript */\nexports.Type = new ExtendedTypeBuilder();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@sinclair/typebox/typebox.js\n");

/***/ })

};
;