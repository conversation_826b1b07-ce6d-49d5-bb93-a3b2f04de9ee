{"name": "ai-paper-reading-assistant", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "tsx prisma/seed.ts", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@langchain/anthropic": "^0.1.19", "@langchain/community": "^0.3.49", "@langchain/core": "^0.1.52", "@langchain/langgraph": "^0.0.19", "@langchain/openai": "^0.0.28", "@pinecone-database/pinecone": "^1.1.2", "@prisma/client": "^5.7.1", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "bcryptjs": "^2.4.3", "chromadb": "^1.7.3", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "date-fns": "^3.0.6", "framer-motion": "^10.16.16", "jsonwebtoken": "^9.0.2", "langchain": "^0.3.30", "langsmith": "^0.1.30", "lru-cache": "^10.1.0", "lucide-react": "^0.303.0", "mammoth": "^1.6.0", "multer": "^1.4.5-lts.1", "next": "^14.2.30", "next-auth": "^4.24.5", "next-themes": "^0.2.1", "openai": "^4.24.1", "pdf-parse": "^1.1.1", "prisma": "^5.7.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.48.2", "react-pdf": "^10.0.1", "recharts": "^2.10.3", "sharp": "^0.33.1", "tailwind-merge": "^2.2.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@testing-library/jest-dom": "^6.2.0", "@testing-library/react": "^14.1.2", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/node": "^20.10.6", "@types/pdf-parse": "^1.1.4", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "@typescript-eslint/eslint-plugin": "^6.17.0", "@typescript-eslint/parser": "^6.17.0", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-config-next": "14.0.4", "husky": "^8.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.2.0", "postcss": "^8.4.32", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.9", "tailwindcss": "^3.4.0", "tsx": "^4.6.2", "typescript": "^5.3.3"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"]}}