import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { RAGSystem } from '@/lib/ai/rag-system';
import { prisma } from '@/lib/db';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { message, conversationId, paperId, paperIds } = body;

    if (!message) {
      return NextResponse.json(
        { error: 'Message is required' },
        { status: 400 }
      );
    }

    // Initialize RAG system
    const ragSystem = new RAGSystem();

    // Get or create conversation
    let conversation;
    if (conversationId) {
      conversation = await prisma.conversation.findFirst({
        where: {
          id: conversationId,
          userId: session.user.id,
        },
      });

      if (!conversation) {
        return NextResponse.json(
          { error: 'Conversation not found' },
          { status: 404 }
        );
      }
    } else {
      // Create new conversation
      conversation = await prisma.conversation.create({
        data: {
          title: message.slice(0, 50) + (message.length > 50 ? '...' : ''),
          userId: session.user.id,
          paperId: paperId || null,
        },
      });
    }

    // Save user message
    await prisma.message.create({
      data: {
        content: message,
        role: 'USER',
        conversationId: conversation.id,
      },
    });

    // Query RAG system
    const ragResponse = await ragSystem.query({
      question: message,
      paperId,
      paperIds,
      maxResults: 5,
    });

    // Save assistant response
    const assistantMessage = await prisma.message.create({
      data: {
        content: ragResponse.answer,
        role: 'ASSISTANT',
        conversationId: conversation.id,
        metadata: JSON.stringify({
          sources: ragResponse.sources,
          confidence: ragResponse.confidence,
        }),
      },
    });

    return NextResponse.json({
      success: true,
      data: {
        conversationId: conversation.id,
        message: {
          id: assistantMessage.id,
          content: ragResponse.answer,
          role: 'ASSISTANT',
          sources: ragResponse.sources,
          confidence: ragResponse.confidence,
          createdAt: assistantMessage.createdAt,
        },
      },
    });
  } catch (error) {
    console.error('Chat error:', error);
    return NextResponse.json(
      { error: 'Failed to process message' },
      { status: 500 }
    );
  }
}

// Get conversation history
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const conversationId = searchParams.get('conversationId');
    const paperId = searchParams.get('paperId');

    if (conversationId) {
      // Get specific conversation with messages
      const conversation = await prisma.conversation.findFirst({
        where: {
          id: conversationId,
          userId: session.user.id,
        },
        include: {
          messages: {
            orderBy: { createdAt: 'asc' },
          },
          paper: {
            select: {
              id: true,
              title: true,
              authors: true,
            },
          },
        },
      });

      if (!conversation) {
        return NextResponse.json(
          { error: 'Conversation not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        data: conversation,
      });
    } else {
      // Get all conversations for user
      const conversations = await prisma.conversation.findMany({
        where: {
          userId: session.user.id,
          ...(paperId && { paperId }),
        },
        include: {
          paper: {
            select: {
              id: true,
              title: true,
              authors: true,
            },
          },
          _count: {
            select: {
              messages: true,
            },
          },
        },
        orderBy: { updatedAt: 'desc' },
        take: 50,
      });

      return NextResponse.json({
        success: true,
        data: conversations,
      });
    }
  } catch (error) {
    console.error('Get conversations error:', error);
    return NextResponse.json(
      { error: 'Failed to get conversations' },
      { status: 500 }
    );
  }
}

// Delete conversation
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const conversationId = searchParams.get('conversationId');

    if (!conversationId) {
      return NextResponse.json(
        { error: 'Conversation ID is required' },
        { status: 400 }
      );
    }

    // Verify ownership and delete
    const deleted = await prisma.conversation.deleteMany({
      where: {
        id: conversationId,
        userId: session.user.id,
      },
    });

    if (deleted.count === 0) {
      return NextResponse.json(
        { error: 'Conversation not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Conversation deleted successfully',
    });
  } catch (error) {
    console.error('Delete conversation error:', error);
    return NextResponse.json(
      { error: 'Failed to delete conversation' },
      { status: 500 }
    );
  }
}
