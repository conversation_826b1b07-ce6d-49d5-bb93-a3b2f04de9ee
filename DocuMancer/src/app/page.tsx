import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  BookOpen, 
  MessageSquare, 
  BarChart3, 
  Upload, 
  Search, 
  Users,
  Zap,
  Shield,
  Globe
} from 'lucide-react'

export default function HomePage() {
  const features = [
    {
      icon: Upload,
      title: 'Smart Document Processing',
      description: 'Upload PDFs, Word docs, and more. Our AI automatically extracts key information and creates searchable embeddings.',
    },
    {
      icon: MessageSquare,
      title: 'Intelligent Q&A',
      description: 'Ask questions about your papers and get accurate, contextual answers powered by advanced RAG technology.',
    },
    {
      icon: BarChart3,
      title: 'Deep Analysis',
      description: 'Generate structured summaries, extract methodologies, and compare multiple papers side by side.',
    },
    {
      icon: Search,
      title: 'Semantic Search',
      description: 'Find relevant information across your entire paper collection using natural language queries.',
    },
    {
      icon: Users,
      title: 'Collaboration',
      description: 'Share papers and insights with your team. Discuss findings and build knowledge together.',
    },
    {
      icon: Zap,
      title: 'AI-Powered Insights',
      description: 'Discover trends, identify gaps, and get personalized recommendations for your research.',
    },
  ]

  const benefits = [
    {
      icon: Shield,
      title: 'Secure & Private',
      description: 'Your research data is encrypted and stored securely. You maintain full control over your papers.',
    },
    {
      icon: Globe,
      title: 'Accessible Anywhere',
      description: 'Access your research library from any device. Responsive design works on desktop and mobile.',
    },
    {
      icon: Zap,
      title: 'Lightning Fast',
      description: 'Powered by cutting-edge AI models and optimized for speed. Get answers in seconds, not minutes.',
    },
  ]

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-muted/20">
      {/* Hero Section */}
      <section className="container mx-auto px-4 py-20 text-center">
        <div className="max-w-4xl mx-auto">
          <div className="flex justify-center mb-6">
            <BookOpen className="h-16 w-16 text-primary" />
          </div>
          <h1 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
            AI-Powered Paper Reading Assistant
          </h1>
          <p className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed">
            Transform how you read, analyze, and understand academic papers. 
            Upload your research, ask questions, and discover insights with the power of AI.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg" className="text-lg px-8 py-6">
              <Link href="/chat">
                Start Chatting
              </Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="text-lg px-8 py-6">
              <Link href="/auth/register">
                Sign Up Free
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="container mx-auto px-4 py-20">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Everything you need for research
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Comprehensive tools to help you read, understand, and analyze academic papers more effectively.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <Card key={index} className="border-border/50 hover:border-border transition-colors">
              <CardHeader>
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-primary/10 rounded-lg">
                    <feature.icon className="h-6 w-6 text-primary" />
                  </div>
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-base leading-relaxed">
                  {feature.description}
                </CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Benefits Section */}
      <section className="bg-muted/30 py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Why choose PaperAI?
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Built for researchers, by researchers. We understand the challenges of academic research.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {benefits.map((benefit, index) => (
              <div key={index} className="text-center">
                <div className="flex justify-center mb-4">
                  <div className="p-4 bg-primary/10 rounded-full">
                    <benefit.icon className="h-8 w-8 text-primary" />
                  </div>
                </div>
                <h3 className="text-xl font-semibold mb-3">{benefit.title}</h3>
                <p className="text-muted-foreground leading-relaxed">
                  {benefit.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="container mx-auto px-4 py-20 text-center">
        <div className="max-w-2xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to revolutionize your research?
          </h2>
          <p className="text-xl text-muted-foreground mb-8">
            Join thousands of researchers who are already using PaperAI to accelerate their work.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg" className="text-lg px-8 py-6">
              <Link href="/auth/register">
                Start Free Trial
              </Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="text-lg px-8 py-6">
              <Link href="/contact">
                Contact Sales
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t bg-background">
        <div className="container mx-auto px-4 py-12">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <BookOpen className="h-6 w-6 text-primary" />
                <span className="font-bold text-xl">PaperAI</span>
              </div>
              <p className="text-muted-foreground">
                AI-powered research assistant for academic papers.
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Product</h3>
              <ul className="space-y-2 text-muted-foreground">
                <li><Link href="/features" className="hover:text-foreground">Features</Link></li>
                <li><Link href="/pricing" className="hover:text-foreground">Pricing</Link></li>
                <li><Link href="/demo" className="hover:text-foreground">Demo</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Support</h3>
              <ul className="space-y-2 text-muted-foreground">
                <li><Link href="/docs" className="hover:text-foreground">Documentation</Link></li>
                <li><Link href="/help" className="hover:text-foreground">Help Center</Link></li>
                <li><Link href="/contact" className="hover:text-foreground">Contact</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Company</h3>
              <ul className="space-y-2 text-muted-foreground">
                <li><Link href="/about" className="hover:text-foreground">About</Link></li>
                <li><Link href="/blog" className="hover:text-foreground">Blog</Link></li>
                <li><Link href="/careers" className="hover:text-foreground">Careers</Link></li>
              </ul>
            </div>
          </div>
          <div className="border-t mt-12 pt-8 text-center text-muted-foreground">
            <p>&copy; 2024 PaperAI. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
